{"/dashboard/page": "app/dashboard/page.js", "/auth/error/page": "app/auth/error/page.js", "/dashboard/novels/[id]/page": "app/dashboard/novels/[id]/page.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/novels/[id]/chapters/route": "app/api/novels/[id]/chapters/route.js", "/api/credits/balance/route": "app/api/credits/balance/route.js", "/dashboard/novels/[id]/chapters/new/page": "app/dashboard/novels/[id]/chapters/new/page.js", "/browse/page": "app/browse/page.js"}