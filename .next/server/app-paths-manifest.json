{"/unauthorized/page": "app/unauthorized/page.js", "/api/credits/balance/route": "app/api/credits/balance/route.js", "/page": "app/page.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/browse/page": "app/browse/page.js", "/api/novels/route": "app/api/novels/route.js", "/dashboard/page": "app/dashboard/page.js", "/api/novels/author/route": "app/api/novels/author/route.js", "/dashboard/novels/[id]/page": "app/dashboard/novels/[id]/page.js", "/api/novels/[id]/route": "app/api/novels/[id]/route.js", "/api/chapters/[id]/publish/route": "app/api/chapters/[id]/publish/route.js", "/_not-found/page": "app/_not-found/page.js", "/dashboard/novels/[id]/chapters/new/page": "app/dashboard/novels/[id]/chapters/new/page.js", "/dashboard/novels/[id]/chapters/[chapterId]/edit/page": "app/dashboard/novels/[id]/chapters/[chapterId]/edit/page.js", "/api/chapters/[id]/route": "app/api/chapters/[id]/route.js"}