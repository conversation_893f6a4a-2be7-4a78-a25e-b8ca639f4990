{"/unauthorized/page": "app/unauthorized/page.js", "/api/credits/balance/route": "app/api/credits/balance/route.js", "/dashboard/novels/[id]/page": "app/dashboard/novels/[id]/page.js", "/dashboard/page": "app/dashboard/page.js", "/api/novels/[id]/chapters/route": "app/api/novels/[id]/chapters/route.js", "/dashboard/novels/[id]/chapters/new/page": "app/dashboard/novels/[id]/chapters/new/page.js", "/dashboard/novels/[id]/chapters/[chapterId]/edit/page": "app/dashboard/novels/[id]/chapters/[chapterId]/edit/page.js"}