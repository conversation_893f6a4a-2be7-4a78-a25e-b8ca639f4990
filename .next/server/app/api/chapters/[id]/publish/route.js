/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chapters/[id]/publish/route";
exports.ids = ["app/api/chapters/[id]/publish/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "./action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute&page=%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute&page=%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_chapters_id_publish_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chapters/[id]/publish/route.ts */ \"(rsc)/./src/app/api/chapters/[id]/publish/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chapters/[id]/publish/route\",\n        pathname: \"/api/chapters/[id]/publish\",\n        filename: \"route\",\n        bundlePath: \"app/api/chapters/[id]/publish/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/app/api/chapters/[id]/publish/route.ts\",\n    nextConfigOutput,\n    userland: _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_chapters_id_publish_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/chapters/[id]/publish/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute&page=%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chapters/[id]/publish/route.ts":
/*!****************************************************!*\
  !*** ./src/app/api/chapters/[id]/publish/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\n\n\nasync function PATCH(request, { params }) {\n    const { id } = params;\n    try {\n        // Check authentication\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user || session.user.role !== \"AUTHOR\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Get chapter and verify ownership using Supabase\n        const { data: chapter, error: chapterError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"chapters\").select(`\n        *,\n        novels!chapters_novel_id_fkey(id, title, author_id, status)\n      `).eq(\"id\", id).single();\n        if (chapterError || !chapter) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Chapter not found\"\n            }, {\n                status: 404\n            });\n        }\n        if (chapter.novels.author_id !== session.user.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"You don't have permission to edit this chapter\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const { status } = body;\n        // Validate status\n        if (!status || !Object.values(ChapterStatus).includes(status)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid status. Must be DRAFT or PUBLISHED.\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate chapter content before publishing\n        if (status === ChapterStatus.PUBLISHED) {\n            if (!chapter.title?.trim() || !chapter.content?.trim()) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Cannot publish chapter with empty title or content\"\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Update chapter status using Supabase\n        const updateData = {\n            status\n        };\n        if (status === \"PUBLISHED\") {\n            updateData.updated_at = new Date().toISOString();\n        }\n        const { data: updatedChapter, error: updateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"chapters\").update(updateData).eq(\"id\", id).select(`\n        *,\n        novels!chapters_novel_id_fkey(id, title, author_id, status)\n      `).single();\n        if (updateError) {\n            console.error(\"Error updating chapter status:\", updateError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to update chapter status\"\n            }, {\n                status: 500\n            });\n        }\n        const actionMessage = status === \"PUBLISHED\" ? \"Chapter published successfully\" : \"Chapter unpublished successfully\";\n        // Transform chapter to match expected format\n        const transformedChapter = {\n            id: updatedChapter.id,\n            title: updatedChapter.title,\n            content: updatedChapter.content,\n            order: updatedChapter.order,\n            status: updatedChapter.status,\n            novelId: updatedChapter.novel_id,\n            createdAt: updatedChapter.created_at,\n            updatedAt: updatedChapter.updated_at,\n            novel: {\n                id: updatedChapter.novels.id,\n                title: updatedChapter.novels.title,\n                authorId: updatedChapter.novels.author_id,\n                status: updatedChapter.novels.status\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: transformedChapter,\n            message: actionMessage\n        });\n    } catch (error) {\n        console.error(\"Error updating chapter status:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to update chapter status\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Batch update multiple chapters' publish status\nasync function POST(request, { params }) {\n    try {\n        // Check authentication\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user || session.user.role !== \"AUTHOR\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { chapterIds, status } = body;\n        // Validate inputs\n        if (!Array.isArray(chapterIds) || chapterIds.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Chapter IDs array is required\"\n            }, {\n                status: 400\n            });\n        }\n        if (!status || ![\n            \"DRAFT\",\n            \"PUBLISHED\"\n        ].includes(status)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid status. Must be DRAFT or PUBLISHED.\"\n            }, {\n                status: 400\n            });\n        }\n        // Verify all chapters belong to the author using Supabase\n        const { data: chapters, error: chaptersError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"chapters\").select(`\n        id,\n        title,\n        content,\n        novels!chapters_novel_id_fkey(author_id)\n      `).in(\"id\", chapterIds);\n        if (chaptersError) {\n            console.error(\"Error fetching chapters:\", chaptersError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch chapters\"\n            }, {\n                status: 500\n            });\n        }\n        // Check if all chapters exist and belong to the author\n        if (chapters.length !== chapterIds.length) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Some chapters not found\"\n            }, {\n                status: 404\n            });\n        }\n        const unauthorizedChapters = chapters.filter((chapter)=>chapter.novels.author_id !== session.user.id);\n        if (unauthorizedChapters.length > 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"You don't have permission to edit some of these chapters\"\n            }, {\n                status: 403\n            });\n        }\n        // Validate content for publishing\n        if (status === \"PUBLISHED\") {\n            const invalidChapters = chapters.filter((chapter)=>!chapter.title?.trim() || !chapter.content?.trim());\n            if (invalidChapters.length > 0) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Cannot publish chapters with empty title or content\"\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Update all chapters using Supabase\n        const updateData = {\n            status\n        };\n        if (status === \"PUBLISHED\") {\n            updateData.updated_at = new Date().toISOString();\n        }\n        const { data: updatedChapters, error: updateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"chapters\").update(updateData).in(\"id\", chapterIds).select(\"id\");\n        if (updateError) {\n            console.error(\"Error batch updating chapter status:\", updateError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to update chapter status\"\n            }, {\n                status: 500\n            });\n        }\n        const updatedCount = updatedChapters?.length || 0;\n        const actionMessage = status === \"PUBLISHED\" ? `${updatedCount} chapters published successfully` : `${updatedCount} chapters unpublished successfully`;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                updatedCount,\n                status\n            },\n            message: actionMessage\n        });\n    } catch (error) {\n        console.error(\"Error batch updating chapter status:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to update chapter status\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9jaGFwdGVycy9baWRdL3B1Ymxpc2gvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF1RDtBQUNYO0FBQ0o7QUFDQztBQVFsQyxlQUFlSSxNQUNwQkMsT0FBb0IsRUFDcEIsRUFBRUMsTUFBTSxFQUFlO0lBRXZCLE1BQU0sRUFBRUMsRUFBRSxFQUFFLEdBQUdEO0lBRWYsSUFBSTtRQUNGLHVCQUF1QjtRQUN2QixNQUFNRSxVQUFVLE1BQU1QLDJEQUFnQkEsQ0FBQ0Msa0RBQVdBO1FBQ2xELElBQUksQ0FBQ00sU0FBU0MsUUFBUUQsUUFBUUMsSUFBSSxDQUFDQyxJQUFJLEtBQUssVUFBVTtZQUNwRCxPQUFPVixxREFBWUEsQ0FBQ1csSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQWUsR0FBRztnQkFBRUMsUUFBUTtZQUFJO1FBQ3BFO1FBRUEsa0RBQWtEO1FBQ2xELE1BQU0sRUFBRUMsTUFBTUMsT0FBTyxFQUFFSCxPQUFPSSxZQUFZLEVBQUUsR0FBRyxNQUFNYixtREFBUUEsQ0FDMURjLElBQUksQ0FBQyxZQUNMQyxNQUFNLENBQUMsQ0FBQzs7O01BR1QsQ0FBQyxFQUNBQyxFQUFFLENBQUMsTUFBTVosSUFDVGEsTUFBTTtRQUVULElBQUlKLGdCQUFnQixDQUFDRCxTQUFTO1lBQzVCLE9BQU9mLHFEQUFZQSxDQUFDVyxJQUFJLENBQUM7Z0JBQUVDLE9BQU87WUFBb0IsR0FBRztnQkFBRUMsUUFBUTtZQUFJO1FBQ3pFO1FBRUEsSUFBSUUsUUFBUU0sTUFBTSxDQUFDQyxTQUFTLEtBQUtkLFFBQVFDLElBQUksQ0FBQ0YsRUFBRSxFQUFFO1lBQ2hELE9BQU9QLHFEQUFZQSxDQUFDVyxJQUFJLENBQ3RCO2dCQUFFQyxPQUFPO1lBQWlELEdBQzFEO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxNQUFNVSxPQUFPLE1BQU1sQixRQUFRTSxJQUFJO1FBQy9CLE1BQU0sRUFBRUUsTUFBTSxFQUFFLEdBQUdVO1FBRW5CLGtCQUFrQjtRQUNsQixJQUFJLENBQUNWLFVBQVUsQ0FBQ1csT0FBT0MsTUFBTSxDQUFDQyxlQUFlQyxRQUFRLENBQUNkLFNBQVM7WUFDN0QsT0FBT2IscURBQVlBLENBQUNXLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87WUFBOEMsR0FDdkQ7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLDZDQUE2QztRQUM3QyxJQUFJQSxXQUFXYSxjQUFjRSxTQUFTLEVBQUU7WUFDdEMsSUFBSSxDQUFDYixRQUFRYyxLQUFLLEVBQUVDLFVBQVUsQ0FBQ2YsUUFBUWdCLE9BQU8sRUFBRUQsUUFBUTtnQkFDdEQsT0FBTzlCLHFEQUFZQSxDQUFDVyxJQUFJLENBQ3RCO29CQUFFQyxPQUFPO2dCQUFxRCxHQUM5RDtvQkFBRUMsUUFBUTtnQkFBSTtZQUVsQjtRQUNGO1FBRUEsdUNBQXVDO1FBQ3ZDLE1BQU1tQixhQUFrQjtZQUFFbkI7UUFBTztRQUNqQyxJQUFJQSxXQUFXLGFBQWE7WUFDMUJtQixXQUFXQyxVQUFVLEdBQUcsSUFBSUMsT0FBT0MsV0FBVztRQUNoRDtRQUVBLE1BQU0sRUFBRXJCLE1BQU1zQixjQUFjLEVBQUV4QixPQUFPeUIsV0FBVyxFQUFFLEdBQUcsTUFBTWxDLG1EQUFRQSxDQUNoRWMsSUFBSSxDQUFDLFlBQ0xxQixNQUFNLENBQUNOLFlBQ1BiLEVBQUUsQ0FBQyxNQUFNWixJQUNUVyxNQUFNLENBQUMsQ0FBQzs7O01BR1QsQ0FBQyxFQUNBRSxNQUFNO1FBRVQsSUFBSWlCLGFBQWE7WUFDZkUsUUFBUTNCLEtBQUssQ0FBQyxrQ0FBa0N5QjtZQUNoRCxPQUFPckMscURBQVlBLENBQUNXLElBQUksQ0FBQztnQkFBRUMsT0FBTztZQUFrQyxHQUFHO2dCQUFFQyxRQUFRO1lBQUk7UUFDdkY7UUFFQSxNQUFNMkIsZ0JBQWdCM0IsV0FBVyxjQUM3QixtQ0FDQTtRQUVKLDZDQUE2QztRQUM3QyxNQUFNNEIscUJBQXFCO1lBQ3pCbEMsSUFBSTZCLGVBQWU3QixFQUFFO1lBQ3JCc0IsT0FBT08sZUFBZVAsS0FBSztZQUMzQkUsU0FBU0ssZUFBZUwsT0FBTztZQUMvQlcsT0FBT04sZUFBZU0sS0FBSztZQUMzQjdCLFFBQVF1QixlQUFldkIsTUFBTTtZQUM3QjhCLFNBQVNQLGVBQWVRLFFBQVE7WUFDaENDLFdBQVdULGVBQWVVLFVBQVU7WUFDcENDLFdBQVdYLGVBQWVILFVBQVU7WUFDcENlLE9BQU87Z0JBQ0x6QyxJQUFJNkIsZUFBZWYsTUFBTSxDQUFDZCxFQUFFO2dCQUM1QnNCLE9BQU9PLGVBQWVmLE1BQU0sQ0FBQ1EsS0FBSztnQkFDbENvQixVQUFVYixlQUFlZixNQUFNLENBQUNDLFNBQVM7Z0JBQ3pDVCxRQUFRdUIsZUFBZWYsTUFBTSxDQUFDUixNQUFNO1lBQ3RDO1FBQ0Y7UUFFQSxPQUFPYixxREFBWUEsQ0FBQ1csSUFBSSxDQUFDO1lBQ3ZCdUMsU0FBUztZQUNUcEMsTUFBTTJCO1lBQ05VLFNBQVNYO1FBQ1g7SUFDRixFQUFFLE9BQU81QixPQUFPO1FBQ2QyQixRQUFRM0IsS0FBSyxDQUFDLGtDQUFrQ0E7UUFDaEQsT0FBT1oscURBQVlBLENBQUNXLElBQUksQ0FDdEI7WUFBRUMsT0FBTztRQUFrQyxHQUMzQztZQUFFQyxRQUFRO1FBQUk7SUFFbEI7QUFDRjtBQUVBLGlEQUFpRDtBQUMxQyxlQUFldUMsS0FDcEIvQyxPQUFvQixFQUNwQixFQUFFQyxNQUFNLEVBQWU7SUFFdkIsSUFBSTtRQUNGLHVCQUF1QjtRQUN2QixNQUFNRSxVQUFVLE1BQU1QLDJEQUFnQkEsQ0FBQ0Msa0RBQVdBO1FBQ2xELElBQUksQ0FBQ00sU0FBU0MsUUFBUUQsUUFBUUMsSUFBSSxDQUFDQyxJQUFJLEtBQUssVUFBVTtZQUNwRCxPQUFPVixxREFBWUEsQ0FBQ1csSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQWUsR0FBRztnQkFBRUMsUUFBUTtZQUFJO1FBQ3BFO1FBRUEsTUFBTVUsT0FBTyxNQUFNbEIsUUFBUU0sSUFBSTtRQUMvQixNQUFNLEVBQUUwQyxVQUFVLEVBQUV4QyxNQUFNLEVBQUUsR0FBR1U7UUFFL0Isa0JBQWtCO1FBQ2xCLElBQUksQ0FBQytCLE1BQU1DLE9BQU8sQ0FBQ0YsZUFBZUEsV0FBV0csTUFBTSxLQUFLLEdBQUc7WUFDekQsT0FBT3hELHFEQUFZQSxDQUFDVyxJQUFJLENBQ3RCO2dCQUFFQyxPQUFPO1lBQWdDLEdBQ3pDO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxJQUFJLENBQUNBLFVBQVUsQ0FBQztZQUFDO1lBQVM7U0FBWSxDQUFDYyxRQUFRLENBQUNkLFNBQVM7WUFDdkQsT0FBT2IscURBQVlBLENBQUNXLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87WUFBOEMsR0FDdkQ7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLDBEQUEwRDtRQUMxRCxNQUFNLEVBQUVDLE1BQU0yQyxRQUFRLEVBQUU3QyxPQUFPOEMsYUFBYSxFQUFFLEdBQUcsTUFBTXZELG1EQUFRQSxDQUM1RGMsSUFBSSxDQUFDLFlBQ0xDLE1BQU0sQ0FBQyxDQUFDOzs7OztNQUtULENBQUMsRUFDQXlDLEVBQUUsQ0FBQyxNQUFNTjtRQUVaLElBQUlLLGVBQWU7WUFDakJuQixRQUFRM0IsS0FBSyxDQUFDLDRCQUE0QjhDO1lBQzFDLE9BQU8xRCxxREFBWUEsQ0FBQ1csSUFBSSxDQUFDO2dCQUFFQyxPQUFPO1lBQTJCLEdBQUc7Z0JBQUVDLFFBQVE7WUFBSTtRQUNoRjtRQUVBLHVEQUF1RDtRQUN2RCxJQUFJNEMsU0FBU0QsTUFBTSxLQUFLSCxXQUFXRyxNQUFNLEVBQUU7WUFDekMsT0FBT3hELHFEQUFZQSxDQUFDVyxJQUFJLENBQ3RCO2dCQUFFQyxPQUFPO1lBQTBCLEdBQ25DO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxNQUFNK0MsdUJBQXVCSCxTQUFTSSxNQUFNLENBQzFDOUMsQ0FBQUEsVUFBV0EsUUFBUU0sTUFBTSxDQUFDQyxTQUFTLEtBQUtkLFFBQVFDLElBQUksQ0FBQ0YsRUFBRTtRQUd6RCxJQUFJcUQscUJBQXFCSixNQUFNLEdBQUcsR0FBRztZQUNuQyxPQUFPeEQscURBQVlBLENBQUNXLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87WUFBMkQsR0FDcEU7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLGtDQUFrQztRQUNsQyxJQUFJQSxXQUFXLGFBQWE7WUFDMUIsTUFBTWlELGtCQUFrQkwsU0FBU0ksTUFBTSxDQUNyQzlDLENBQUFBLFVBQVcsQ0FBQ0EsUUFBUWMsS0FBSyxFQUFFQyxVQUFVLENBQUNmLFFBQVFnQixPQUFPLEVBQUVEO1lBR3pELElBQUlnQyxnQkFBZ0JOLE1BQU0sR0FBRyxHQUFHO2dCQUM5QixPQUFPeEQscURBQVlBLENBQUNXLElBQUksQ0FDdEI7b0JBQUVDLE9BQU87Z0JBQXNELEdBQy9EO29CQUFFQyxRQUFRO2dCQUFJO1lBRWxCO1FBQ0Y7UUFFQSxxQ0FBcUM7UUFDckMsTUFBTW1CLGFBQWtCO1lBQUVuQjtRQUFPO1FBQ2pDLElBQUlBLFdBQVcsYUFBYTtZQUMxQm1CLFdBQVdDLFVBQVUsR0FBRyxJQUFJQyxPQUFPQyxXQUFXO1FBQ2hEO1FBRUEsTUFBTSxFQUFFckIsTUFBTWlELGVBQWUsRUFBRW5ELE9BQU95QixXQUFXLEVBQUUsR0FBRyxNQUFNbEMsbURBQVFBLENBQ2pFYyxJQUFJLENBQUMsWUFDTHFCLE1BQU0sQ0FBQ04sWUFDUDJCLEVBQUUsQ0FBQyxNQUFNTixZQUNUbkMsTUFBTSxDQUFDO1FBRVYsSUFBSW1CLGFBQWE7WUFDZkUsUUFBUTNCLEtBQUssQ0FBQyx3Q0FBd0N5QjtZQUN0RCxPQUFPckMscURBQVlBLENBQUNXLElBQUksQ0FBQztnQkFBRUMsT0FBTztZQUFrQyxHQUFHO2dCQUFFQyxRQUFRO1lBQUk7UUFDdkY7UUFFQSxNQUFNbUQsZUFBZUQsaUJBQWlCUCxVQUFVO1FBQ2hELE1BQU1oQixnQkFBZ0IzQixXQUFXLGNBQzdCLENBQUMsRUFBRW1ELGFBQWEsZ0NBQWdDLENBQUMsR0FDakQsQ0FBQyxFQUFFQSxhQUFhLGtDQUFrQyxDQUFDO1FBRXZELE9BQU9oRSxxREFBWUEsQ0FBQ1csSUFBSSxDQUFDO1lBQ3ZCdUMsU0FBUztZQUNUcEMsTUFBTTtnQkFDSmtEO2dCQUNBbkQ7WUFDRjtZQUNBc0MsU0FBU1g7UUFDWDtJQUNGLEVBQUUsT0FBTzVCLE9BQU87UUFDZDJCLFFBQVEzQixLQUFLLENBQUMsd0NBQXdDQTtRQUN0RCxPQUFPWixxREFBWUEsQ0FBQ1csSUFBSSxDQUN0QjtZQUFFQyxPQUFPO1FBQWtDLEdBQzNDO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmxhY2stYmxvZy8uL3NyYy9hcHAvYXBpL2NoYXB0ZXJzL1tpZF0vcHVibGlzaC9yb3V0ZS50cz9jZDM3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tIFwibmV4dC9zZXJ2ZXJcIlxuaW1wb3J0IHsgZ2V0U2VydmVyU2Vzc2lvbiB9IGZyb20gXCJuZXh0LWF1dGhcIlxuaW1wb3J0IHsgYXV0aE9wdGlvbnMgfSBmcm9tIFwiQC9saWIvYXV0aFwiXG5pbXBvcnQgeyBzdXBhYmFzZSB9IGZyb20gXCJAL2xpYi9zdXBhYmFzZVwiXG5cbmludGVyZmFjZSBSb3V0ZVBhcmFtcyB7XG4gIHBhcmFtczoge1xuICAgIGlkOiBzdHJpbmdcbiAgfVxufVxuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUEFUQ0goXG4gIHJlcXVlc3Q6IE5leHRSZXF1ZXN0LFxuICB7IHBhcmFtcyB9OiBSb3V0ZVBhcmFtc1xuKSB7XG4gIGNvbnN0IHsgaWQgfSA9IHBhcmFtc1xuXG4gIHRyeSB7XG4gICAgLy8gQ2hlY2sgYXV0aGVudGljYXRpb25cbiAgICBjb25zdCBzZXNzaW9uID0gYXdhaXQgZ2V0U2VydmVyU2Vzc2lvbihhdXRoT3B0aW9ucylcbiAgICBpZiAoIXNlc3Npb24/LnVzZXIgfHwgc2Vzc2lvbi51c2VyLnJvbGUgIT09IFwiQVVUSE9SXCIpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiBcIlVuYXV0aG9yaXplZFwiIH0sIHsgc3RhdHVzOiA0MDEgfSlcbiAgICB9XG5cbiAgICAvLyBHZXQgY2hhcHRlciBhbmQgdmVyaWZ5IG93bmVyc2hpcCB1c2luZyBTdXBhYmFzZVxuICAgIGNvbnN0IHsgZGF0YTogY2hhcHRlciwgZXJyb3I6IGNoYXB0ZXJFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjaGFwdGVycycpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgKixcbiAgICAgICAgbm92ZWxzIWNoYXB0ZXJzX25vdmVsX2lkX2ZrZXkoaWQsIHRpdGxlLCBhdXRob3JfaWQsIHN0YXR1cylcbiAgICAgIGApXG4gICAgICAuZXEoJ2lkJywgaWQpXG4gICAgICAuc2luZ2xlKClcblxuICAgIGlmIChjaGFwdGVyRXJyb3IgfHwgIWNoYXB0ZXIpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiBcIkNoYXB0ZXIgbm90IGZvdW5kXCIgfSwgeyBzdGF0dXM6IDQwNCB9KVxuICAgIH1cblxuICAgIGlmIChjaGFwdGVyLm5vdmVscy5hdXRob3JfaWQgIT09IHNlc3Npb24udXNlci5pZCkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiBcIllvdSBkb24ndCBoYXZlIHBlcm1pc3Npb24gdG8gZWRpdCB0aGlzIGNoYXB0ZXJcIiB9LFxuICAgICAgICB7IHN0YXR1czogNDAzIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICBjb25zdCBib2R5ID0gYXdhaXQgcmVxdWVzdC5qc29uKClcbiAgICBjb25zdCB7IHN0YXR1cyB9ID0gYm9keVxuXG4gICAgLy8gVmFsaWRhdGUgc3RhdHVzXG4gICAgaWYgKCFzdGF0dXMgfHwgIU9iamVjdC52YWx1ZXMoQ2hhcHRlclN0YXR1cykuaW5jbHVkZXMoc3RhdHVzKSkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiBcIkludmFsaWQgc3RhdHVzLiBNdXN0IGJlIERSQUZUIG9yIFBVQkxJU0hFRC5cIiB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICAvLyBWYWxpZGF0ZSBjaGFwdGVyIGNvbnRlbnQgYmVmb3JlIHB1Ymxpc2hpbmdcbiAgICBpZiAoc3RhdHVzID09PSBDaGFwdGVyU3RhdHVzLlBVQkxJU0hFRCkge1xuICAgICAgaWYgKCFjaGFwdGVyLnRpdGxlPy50cmltKCkgfHwgIWNoYXB0ZXIuY29udGVudD8udHJpbSgpKSB7XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgICB7IGVycm9yOiBcIkNhbm5vdCBwdWJsaXNoIGNoYXB0ZXIgd2l0aCBlbXB0eSB0aXRsZSBvciBjb250ZW50XCIgfSxcbiAgICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICAgKVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIFVwZGF0ZSBjaGFwdGVyIHN0YXR1cyB1c2luZyBTdXBhYmFzZVxuICAgIGNvbnN0IHVwZGF0ZURhdGE6IGFueSA9IHsgc3RhdHVzIH1cbiAgICBpZiAoc3RhdHVzID09PSBcIlBVQkxJU0hFRFwiKSB7XG4gICAgICB1cGRhdGVEYXRhLnVwZGF0ZWRfYXQgPSBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICB9XG5cbiAgICBjb25zdCB7IGRhdGE6IHVwZGF0ZWRDaGFwdGVyLCBlcnJvcjogdXBkYXRlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnY2hhcHRlcnMnKVxuICAgICAgLnVwZGF0ZSh1cGRhdGVEYXRhKVxuICAgICAgLmVxKCdpZCcsIGlkKVxuICAgICAgLnNlbGVjdChgXG4gICAgICAgICosXG4gICAgICAgIG5vdmVscyFjaGFwdGVyc19ub3ZlbF9pZF9ma2V5KGlkLCB0aXRsZSwgYXV0aG9yX2lkLCBzdGF0dXMpXG4gICAgICBgKVxuICAgICAgLnNpbmdsZSgpXG5cbiAgICBpZiAodXBkYXRlRXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciB1cGRhdGluZyBjaGFwdGVyIHN0YXR1czpcIiwgdXBkYXRlRXJyb3IpXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogXCJGYWlsZWQgdG8gdXBkYXRlIGNoYXB0ZXIgc3RhdHVzXCIgfSwgeyBzdGF0dXM6IDUwMCB9KVxuICAgIH1cblxuICAgIGNvbnN0IGFjdGlvbk1lc3NhZ2UgPSBzdGF0dXMgPT09IFwiUFVCTElTSEVEXCJcbiAgICAgID8gXCJDaGFwdGVyIHB1Ymxpc2hlZCBzdWNjZXNzZnVsbHlcIlxuICAgICAgOiBcIkNoYXB0ZXIgdW5wdWJsaXNoZWQgc3VjY2Vzc2Z1bGx5XCJcblxuICAgIC8vIFRyYW5zZm9ybSBjaGFwdGVyIHRvIG1hdGNoIGV4cGVjdGVkIGZvcm1hdFxuICAgIGNvbnN0IHRyYW5zZm9ybWVkQ2hhcHRlciA9IHtcbiAgICAgIGlkOiB1cGRhdGVkQ2hhcHRlci5pZCxcbiAgICAgIHRpdGxlOiB1cGRhdGVkQ2hhcHRlci50aXRsZSxcbiAgICAgIGNvbnRlbnQ6IHVwZGF0ZWRDaGFwdGVyLmNvbnRlbnQsXG4gICAgICBvcmRlcjogdXBkYXRlZENoYXB0ZXIub3JkZXIsXG4gICAgICBzdGF0dXM6IHVwZGF0ZWRDaGFwdGVyLnN0YXR1cyxcbiAgICAgIG5vdmVsSWQ6IHVwZGF0ZWRDaGFwdGVyLm5vdmVsX2lkLFxuICAgICAgY3JlYXRlZEF0OiB1cGRhdGVkQ2hhcHRlci5jcmVhdGVkX2F0LFxuICAgICAgdXBkYXRlZEF0OiB1cGRhdGVkQ2hhcHRlci51cGRhdGVkX2F0LFxuICAgICAgbm92ZWw6IHtcbiAgICAgICAgaWQ6IHVwZGF0ZWRDaGFwdGVyLm5vdmVscy5pZCxcbiAgICAgICAgdGl0bGU6IHVwZGF0ZWRDaGFwdGVyLm5vdmVscy50aXRsZSxcbiAgICAgICAgYXV0aG9ySWQ6IHVwZGF0ZWRDaGFwdGVyLm5vdmVscy5hdXRob3JfaWQsXG4gICAgICAgIHN0YXR1czogdXBkYXRlZENoYXB0ZXIubm92ZWxzLnN0YXR1cyxcbiAgICAgIH0sXG4gICAgfVxuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBkYXRhOiB0cmFuc2Zvcm1lZENoYXB0ZXIsXG4gICAgICBtZXNzYWdlOiBhY3Rpb25NZXNzYWdlLFxuICAgIH0pXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIHVwZGF0aW5nIGNoYXB0ZXIgc3RhdHVzOlwiLCBlcnJvcilcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiBcIkZhaWxlZCB0byB1cGRhdGUgY2hhcHRlciBzdGF0dXNcIiB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKVxuICB9XG59XG5cbi8vIEJhdGNoIHVwZGF0ZSBtdWx0aXBsZSBjaGFwdGVycycgcHVibGlzaCBzdGF0dXNcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKFxuICByZXF1ZXN0OiBOZXh0UmVxdWVzdCxcbiAgeyBwYXJhbXMgfTogUm91dGVQYXJhbXNcbikge1xuICB0cnkge1xuICAgIC8vIENoZWNrIGF1dGhlbnRpY2F0aW9uXG4gICAgY29uc3Qgc2Vzc2lvbiA9IGF3YWl0IGdldFNlcnZlclNlc3Npb24oYXV0aE9wdGlvbnMpXG4gICAgaWYgKCFzZXNzaW9uPy51c2VyIHx8IHNlc3Npb24udXNlci5yb2xlICE9PSBcIkFVVEhPUlwiKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogXCJVbmF1dGhvcml6ZWRcIiB9LCB7IHN0YXR1czogNDAxIH0pXG4gICAgfVxuXG4gICAgY29uc3QgYm9keSA9IGF3YWl0IHJlcXVlc3QuanNvbigpXG4gICAgY29uc3QgeyBjaGFwdGVySWRzLCBzdGF0dXMgfSA9IGJvZHlcblxuICAgIC8vIFZhbGlkYXRlIGlucHV0c1xuICAgIGlmICghQXJyYXkuaXNBcnJheShjaGFwdGVySWRzKSB8fCBjaGFwdGVySWRzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiBcIkNoYXB0ZXIgSURzIGFycmF5IGlzIHJlcXVpcmVkXCIgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApXG4gICAgfVxuXG4gICAgaWYgKCFzdGF0dXMgfHwgIVtcIkRSQUZUXCIsIFwiUFVCTElTSEVEXCJdLmluY2x1ZGVzKHN0YXR1cykpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogXCJJbnZhbGlkIHN0YXR1cy4gTXVzdCBiZSBEUkFGVCBvciBQVUJMSVNIRUQuXCIgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApXG4gICAgfVxuXG4gICAgLy8gVmVyaWZ5IGFsbCBjaGFwdGVycyBiZWxvbmcgdG8gdGhlIGF1dGhvciB1c2luZyBTdXBhYmFzZVxuICAgIGNvbnN0IHsgZGF0YTogY2hhcHRlcnMsIGVycm9yOiBjaGFwdGVyc0Vycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2NoYXB0ZXJzJylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICBpZCxcbiAgICAgICAgdGl0bGUsXG4gICAgICAgIGNvbnRlbnQsXG4gICAgICAgIG5vdmVscyFjaGFwdGVyc19ub3ZlbF9pZF9ma2V5KGF1dGhvcl9pZClcbiAgICAgIGApXG4gICAgICAuaW4oJ2lkJywgY2hhcHRlcklkcylcblxuICAgIGlmIChjaGFwdGVyc0Vycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZmV0Y2hpbmcgY2hhcHRlcnM6XCIsIGNoYXB0ZXJzRXJyb3IpXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogXCJGYWlsZWQgdG8gZmV0Y2ggY2hhcHRlcnNcIiB9LCB7IHN0YXR1czogNTAwIH0pXG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgaWYgYWxsIGNoYXB0ZXJzIGV4aXN0IGFuZCBiZWxvbmcgdG8gdGhlIGF1dGhvclxuICAgIGlmIChjaGFwdGVycy5sZW5ndGggIT09IGNoYXB0ZXJJZHMubGVuZ3RoKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6IFwiU29tZSBjaGFwdGVycyBub3QgZm91bmRcIiB9LFxuICAgICAgICB7IHN0YXR1czogNDA0IH1cbiAgICAgIClcbiAgICB9XG5cbiAgICBjb25zdCB1bmF1dGhvcml6ZWRDaGFwdGVycyA9IGNoYXB0ZXJzLmZpbHRlcihcbiAgICAgIGNoYXB0ZXIgPT4gY2hhcHRlci5ub3ZlbHMuYXV0aG9yX2lkICE9PSBzZXNzaW9uLnVzZXIuaWRcbiAgICApXG5cbiAgICBpZiAodW5hdXRob3JpemVkQ2hhcHRlcnMubGVuZ3RoID4gMCkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiBcIllvdSBkb24ndCBoYXZlIHBlcm1pc3Npb24gdG8gZWRpdCBzb21lIG9mIHRoZXNlIGNoYXB0ZXJzXCIgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMyB9XG4gICAgICApXG4gICAgfVxuXG4gICAgLy8gVmFsaWRhdGUgY29udGVudCBmb3IgcHVibGlzaGluZ1xuICAgIGlmIChzdGF0dXMgPT09IFwiUFVCTElTSEVEXCIpIHtcbiAgICAgIGNvbnN0IGludmFsaWRDaGFwdGVycyA9IGNoYXB0ZXJzLmZpbHRlcihcbiAgICAgICAgY2hhcHRlciA9PiAhY2hhcHRlci50aXRsZT8udHJpbSgpIHx8ICFjaGFwdGVyLmNvbnRlbnQ/LnRyaW0oKVxuICAgICAgKVxuXG4gICAgICBpZiAoaW52YWxpZENoYXB0ZXJzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICAgIHsgZXJyb3I6IFwiQ2Fubm90IHB1Ymxpc2ggY2hhcHRlcnMgd2l0aCBlbXB0eSB0aXRsZSBvciBjb250ZW50XCIgfSxcbiAgICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICAgKVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIFVwZGF0ZSBhbGwgY2hhcHRlcnMgdXNpbmcgU3VwYWJhc2VcbiAgICBjb25zdCB1cGRhdGVEYXRhOiBhbnkgPSB7IHN0YXR1cyB9XG4gICAgaWYgKHN0YXR1cyA9PT0gXCJQVUJMSVNIRURcIikge1xuICAgICAgdXBkYXRlRGF0YS51cGRhdGVkX2F0ID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgfVxuXG4gICAgY29uc3QgeyBkYXRhOiB1cGRhdGVkQ2hhcHRlcnMsIGVycm9yOiB1cGRhdGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdjaGFwdGVycycpXG4gICAgICAudXBkYXRlKHVwZGF0ZURhdGEpXG4gICAgICAuaW4oJ2lkJywgY2hhcHRlcklkcylcbiAgICAgIC5zZWxlY3QoJ2lkJylcblxuICAgIGlmICh1cGRhdGVFcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGJhdGNoIHVwZGF0aW5nIGNoYXB0ZXIgc3RhdHVzOlwiLCB1cGRhdGVFcnJvcilcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGVycm9yOiBcIkZhaWxlZCB0byB1cGRhdGUgY2hhcHRlciBzdGF0dXNcIiB9LCB7IHN0YXR1czogNTAwIH0pXG4gICAgfVxuXG4gICAgY29uc3QgdXBkYXRlZENvdW50ID0gdXBkYXRlZENoYXB0ZXJzPy5sZW5ndGggfHwgMFxuICAgIGNvbnN0IGFjdGlvbk1lc3NhZ2UgPSBzdGF0dXMgPT09IFwiUFVCTElTSEVEXCJcbiAgICAgID8gYCR7dXBkYXRlZENvdW50fSBjaGFwdGVycyBwdWJsaXNoZWQgc3VjY2Vzc2Z1bGx5YFxuICAgICAgOiBgJHt1cGRhdGVkQ291bnR9IGNoYXB0ZXJzIHVucHVibGlzaGVkIHN1Y2Nlc3NmdWxseWBcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgZGF0YToge1xuICAgICAgICB1cGRhdGVkQ291bnQsXG4gICAgICAgIHN0YXR1cyxcbiAgICAgIH0sXG4gICAgICBtZXNzYWdlOiBhY3Rpb25NZXNzYWdlLFxuICAgIH0pXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIGJhdGNoIHVwZGF0aW5nIGNoYXB0ZXIgc3RhdHVzOlwiLCBlcnJvcilcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiBcIkZhaWxlZCB0byB1cGRhdGUgY2hhcHRlciBzdGF0dXNcIiB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKVxuICB9XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiZ2V0U2VydmVyU2Vzc2lvbiIsImF1dGhPcHRpb25zIiwic3VwYWJhc2UiLCJQQVRDSCIsInJlcXVlc3QiLCJwYXJhbXMiLCJpZCIsInNlc3Npb24iLCJ1c2VyIiwicm9sZSIsImpzb24iLCJlcnJvciIsInN0YXR1cyIsImRhdGEiLCJjaGFwdGVyIiwiY2hhcHRlckVycm9yIiwiZnJvbSIsInNlbGVjdCIsImVxIiwic2luZ2xlIiwibm92ZWxzIiwiYXV0aG9yX2lkIiwiYm9keSIsIk9iamVjdCIsInZhbHVlcyIsIkNoYXB0ZXJTdGF0dXMiLCJpbmNsdWRlcyIsIlBVQkxJU0hFRCIsInRpdGxlIiwidHJpbSIsImNvbnRlbnQiLCJ1cGRhdGVEYXRhIiwidXBkYXRlZF9hdCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInVwZGF0ZWRDaGFwdGVyIiwidXBkYXRlRXJyb3IiLCJ1cGRhdGUiLCJjb25zb2xlIiwiYWN0aW9uTWVzc2FnZSIsInRyYW5zZm9ybWVkQ2hhcHRlciIsIm9yZGVyIiwibm92ZWxJZCIsIm5vdmVsX2lkIiwiY3JlYXRlZEF0IiwiY3JlYXRlZF9hdCIsInVwZGF0ZWRBdCIsIm5vdmVsIiwiYXV0aG9ySWQiLCJzdWNjZXNzIiwibWVzc2FnZSIsIlBPU1QiLCJjaGFwdGVySWRzIiwiQXJyYXkiLCJpc0FycmF5IiwibGVuZ3RoIiwiY2hhcHRlcnMiLCJjaGFwdGVyc0Vycm9yIiwiaW4iLCJ1bmF1dGhvcml6ZWRDaGFwdGVycyIsImZpbHRlciIsImludmFsaWRDaGFwdGVycyIsInVwZGF0ZWRDaGFwdGVycyIsInVwZGF0ZWRDb3VudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chapters/[id]/publish/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async session ({ session, user }) {\n            if (session.user) {\n                session.user.id = user.id;\n                session.user.role = user.role || \"READER\";\n            }\n            return session;\n        },\n        async signIn ({ user, account, profile }) {\n            // Allow sign in - user creation will be handled in API endpoints if needed\n            return true;\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    session: {\n        strategy: \"database\"\n    },\n    events: {\n        async signIn ({ user, account, profile }) {\n            console.log(\"User signed in:\", {\n                user: user.email,\n                provider: account?.provider\n            });\n        },\n        async signOut ({ session }) {\n            console.log(\"User signed out:\", session?.user?.email);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9zcmMvbGliL2RiLnRzPzllNGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteCoverImage: () => (/* binding */ deleteCoverImage),\n/* harmony export */   getCoverImageUrl: () => (/* binding */ getCoverImageUrl),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   uploadCoverImage: () => (/* binding */ uploadCoverImage)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://itjoywrqviaonrgtcicv.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml0am95d3Jxdmlhb25yZ3RjaWN2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjAzODQsImV4cCI6MjA2NzYzNjM4NH0.wvD0u8gQOV6yc6gbAqUiYczSHaDvkj9PSC6liT4dfeM\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables\");\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// File upload utilities\nasync function uploadCoverImage(file, novelId) {\n    const fileExt = file.name.split(\".\").pop()?.toLowerCase();\n    const fileName = `${novelId}.${fileExt}`;\n    // Validate file type\n    const allowedTypes = [\n        \"jpg\",\n        \"jpeg\",\n        \"png\",\n        \"webp\"\n    ];\n    if (!fileExt || !allowedTypes.includes(fileExt)) {\n        throw new Error(\"Invalid file type. Only JPG, PNG, and WebP files are allowed.\");\n    }\n    // Validate file size (max 5MB)\n    const maxSize = 5 * 1024 * 1024 // 5MB in bytes\n    ;\n    if (file.size > maxSize) {\n        throw new Error(\"File size too large. Maximum size is 5MB.\");\n    }\n    const { data, error } = await supabase.storage.from(\"covers\").upload(fileName, file, {\n        upsert: true,\n        contentType: file.type\n    });\n    if (error) {\n        throw new Error(`Upload failed: ${error.message}`);\n    }\n    // Get public URL\n    const { data: { publicUrl } } = supabase.storage.from(\"covers\").getPublicUrl(fileName);\n    return publicUrl;\n}\nasync function deleteCoverImage(fileName) {\n    const { error } = await supabase.storage.from(\"covers\").remove([\n        fileName\n    ]);\n    if (error) {\n        throw new Error(`Delete failed: ${error.message}`);\n    }\n}\nfunction getCoverImageUrl(fileName) {\n    const { data: { publicUrl } } = supabase.storage.from(\"covers\").getPublicUrl(fileName);\n    return publicUrl;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute&page=%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchapters%2F%5Bid%5D%2Fpublish%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();