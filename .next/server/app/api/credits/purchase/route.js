"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/credits/purchase/route";
exports.ids = ["app/api/credits/purchase/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "./action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("async_hooks");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("fs/promises");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("process");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "prettier/plugins/html":
/*!****************************************!*\
  !*** external "prettier/plugins/html" ***!
  \****************************************/
/***/ ((module) => {

module.exports = import("prettier/plugins/html");;

/***/ }),

/***/ "prettier/standalone":
/*!**************************************!*\
  !*** external "prettier/standalone" ***!
  \**************************************/
/***/ ((module) => {

module.exports = import("prettier/standalone");;

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcredits%2Fpurchase%2Froute&page=%2Fapi%2Fcredits%2Fpurchase%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcredits%2Fpurchase%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcredits%2Fpurchase%2Froute&page=%2Fapi%2Fcredits%2Fpurchase%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcredits%2Fpurchase%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_credits_purchase_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/credits/purchase/route.ts */ \"(rsc)/./src/app/api/credits/purchase/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/credits/purchase/route\",\n        pathname: \"/api/credits/purchase\",\n        filename: \"route\",\n        bundlePath: \"app/api/credits/purchase/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/app/api/credits/purchase/route.ts\",\n    nextConfigOutput,\n    userland: _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_credits_purchase_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/credits/purchase/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcredits%2Fpurchase%2Froute&page=%2Fapi%2Fcredits%2Fpurchase%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcredits%2Fpurchase%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/credits/purchase/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/credits/purchase/route.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _lib_payment_credit_payment_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/payment/credit-payment-service */ \"(rsc)/./src/lib/payment/credit-payment-service.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\n\n\n\n\n\n\nconst purchaseCreditsSchema = zod__WEBPACK_IMPORTED_MODULE_5__.object({\n    packageId: zod__WEBPACK_IMPORTED_MODULE_5__.string(),\n    paymentMethodId: zod__WEBPACK_IMPORTED_MODULE_5__.string().optional(),\n    savePaymentMethod: zod__WEBPACK_IMPORTED_MODULE_5__.boolean().optional().default(false),\n    useStoredPaymentMethod: zod__WEBPACK_IMPORTED_MODULE_5__.boolean().optional().default(false),\n    storedPaymentMethodId: zod__WEBPACK_IMPORTED_MODULE_5__.string().optional()\n});\n// POST /api/credits/purchase - Purchase credits\nasync function POST(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const purchaseRequest = purchaseCreditsSchema.parse(body);\n        // Validate payment method requirements\n        if (!purchaseRequest.paymentMethodId && !(purchaseRequest.useStoredPaymentMethod && purchaseRequest.storedPaymentMethodId)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Payment method required\",\n                code: \"PAYMENT_METHOD_REQUIRED\"\n            }, {\n                status: 400\n            });\n        }\n        // Get user\n        const user = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: session.user.id\n            },\n            select: {\n                id: true,\n                email: true,\n                stripeCustomerId: true\n            }\n        });\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"User not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Get or create Stripe customer\n        const stripeCustomerId = await _lib_payment_credit_payment_service__WEBPACK_IMPORTED_MODULE_4__.CreditPaymentService.getOrCreateStripeCustomer(user.id, user.email);\n        // Process payment using enhanced service\n        const result = await _lib_payment_credit_payment_service__WEBPACK_IMPORTED_MODULE_4__.CreditPaymentService.createPaymentIntent(purchaseRequest, user.id, stripeCustomerId);\n        if (result.status === \"failed\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: result.error || \"Payment processing failed\",\n                code: \"PAYMENT_FAILED\"\n            }, {\n                status: 400\n            });\n        }\n        if (result.status === \"requires_payment_method\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: result.error || \"Payment method required\",\n                code: \"PAYMENT_METHOD_REQUIRED\"\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            purchase: result.purchase,\n            clientSecret: result.clientSecret,\n            requiresAction: result.requiresAction,\n            status: result.status\n        });\n    } catch (error) {\n        console.error(\"Error purchasing credits:\", error);\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_6__.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid request data\",\n                details: error.errors,\n                code: \"VALIDATION_ERROR\"\n            }, {\n                status: 400\n            });\n        }\n        // Handle specific error types\n        if (error instanceof Error) {\n            if (error.message.includes(\"insufficient_funds\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Insufficient funds\",\n                    code: \"INSUFFICIENT_FUNDS\"\n                }, {\n                    status: 400\n                });\n            }\n            if (error.message.includes(\"card_declined\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Your card was declined\",\n                    code: \"CARD_DECLINED\"\n                }, {\n                    status: 400\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            code: \"INTERNAL_ERROR\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/credits/purchase/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async session ({ session, user }) {\n            if (session.user) {\n                session.user.id = user.id;\n                session.user.role = user.role || \"READER\";\n            }\n            return session;\n        },\n        async signIn ({ user, account, profile }) {\n            // Allow sign in - user creation will be handled in API endpoints if needed\n            return true;\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    session: {\n        strategy: \"database\"\n    },\n    events: {\n        async signIn ({ user, account, profile }) {\n            console.log(\"User signed in:\", {\n                user: user.email,\n                provider: account?.provider\n            });\n        },\n        async signOut ({ session }) {\n            console.log(\"User signed out:\", session?.user?.email);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2F1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUN5RDtBQUNGO0FBQ3RCO0FBRTFCLE1BQU1HLGNBQStCO0lBQzFDQyxTQUFTSix3RUFBYUEsQ0FBQ0UsMkNBQU1BO0lBQzdCRyxXQUFXO1FBQ1RKLHNFQUFjQSxDQUFDO1lBQ2JLLFVBQVVDLFFBQVFDLEdBQUcsQ0FBQ0MsZ0JBQWdCO1lBQ3RDQyxjQUFjSCxRQUFRQyxHQUFHLENBQUNHLG9CQUFvQjtRQUNoRDtLQUNEO0lBQ0RDLFdBQVc7UUFDVCxNQUFNQyxTQUFRLEVBQUVBLE9BQU8sRUFBRUMsSUFBSSxFQUFFO1lBQzdCLElBQUlELFFBQVFDLElBQUksRUFBRTtnQkFDaEJELFFBQVFDLElBQUksQ0FBQ0MsRUFBRSxHQUFHRCxLQUFLQyxFQUFFO2dCQUN6QkYsUUFBUUMsSUFBSSxDQUFDRSxJQUFJLEdBQUcsS0FBY0EsSUFBSSxJQUFJO1lBQzVDO1lBQ0EsT0FBT0g7UUFDVDtRQUNBLE1BQU1JLFFBQU8sRUFBRUgsSUFBSSxFQUFFSSxPQUFPLEVBQUVDLE9BQU8sRUFBRTtZQUNyQywyRUFBMkU7WUFDM0UsT0FBTztRQUNUO1FBQ0EsTUFBTUMsS0FBSSxFQUFFQyxLQUFLLEVBQUVQLElBQUksRUFBRTtZQUN2QixJQUFJQSxNQUFNO2dCQUNSTyxNQUFNTCxJQUFJLEdBQUcsS0FBY0EsSUFBSTtZQUNqQztZQUNBLE9BQU9LO1FBQ1Q7SUFDRjtJQUNBQyxPQUFPO1FBQ0xMLFFBQVE7UUFDUk0sT0FBTztJQUNUO0lBQ0FWLFNBQVM7UUFDUFcsVUFBVTtJQUNaO0lBQ0FDLFFBQVE7UUFDTixNQUFNUixRQUFPLEVBQUVILElBQUksRUFBRUksT0FBTyxFQUFFQyxPQUFPLEVBQUU7WUFDckNPLFFBQVFDLEdBQUcsQ0FBQyxtQkFBbUI7Z0JBQUViLE1BQU1BLEtBQUtjLEtBQUs7Z0JBQUVDLFVBQVVYLFNBQVNXO1lBQVM7UUFDakY7UUFDQSxNQUFNQyxTQUFRLEVBQUVqQixPQUFPLEVBQUU7WUFDdkJhLFFBQVFDLEdBQUcsQ0FBQyxvQkFBb0JkLFNBQVNDLE1BQU1jO1FBQ2pEO0lBQ0Y7QUFDRixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmxhY2stYmxvZy8uL3NyYy9saWIvYXV0aC50cz82NjkyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRBdXRoT3B0aW9ucyB9IGZyb20gXCJuZXh0LWF1dGhcIlxuaW1wb3J0IHsgUHJpc21hQWRhcHRlciB9IGZyb20gXCJAbmV4dC1hdXRoL3ByaXNtYS1hZGFwdGVyXCJcbmltcG9ydCBHb29nbGVQcm92aWRlciBmcm9tIFwibmV4dC1hdXRoL3Byb3ZpZGVycy9nb29nbGVcIlxuaW1wb3J0IHsgcHJpc21hIH0gZnJvbSBcIkAvbGliL2RiXCJcblxuZXhwb3J0IGNvbnN0IGF1dGhPcHRpb25zOiBOZXh0QXV0aE9wdGlvbnMgPSB7XG4gIGFkYXB0ZXI6IFByaXNtYUFkYXB0ZXIocHJpc21hKSxcbiAgcHJvdmlkZXJzOiBbXG4gICAgR29vZ2xlUHJvdmlkZXIoe1xuICAgICAgY2xpZW50SWQ6IHByb2Nlc3MuZW52LkdPT0dMRV9DTElFTlRfSUQhLFxuICAgICAgY2xpZW50U2VjcmV0OiBwcm9jZXNzLmVudi5HT09HTEVfQ0xJRU5UX1NFQ1JFVCEsXG4gICAgfSksXG4gIF0sXG4gIGNhbGxiYWNrczoge1xuICAgIGFzeW5jIHNlc3Npb24oeyBzZXNzaW9uLCB1c2VyIH0pIHtcbiAgICAgIGlmIChzZXNzaW9uLnVzZXIpIHtcbiAgICAgICAgc2Vzc2lvbi51c2VyLmlkID0gdXNlci5pZFxuICAgICAgICBzZXNzaW9uLnVzZXIucm9sZSA9ICh1c2VyIGFzIGFueSkucm9sZSB8fCBcIlJFQURFUlwiXG4gICAgICB9XG4gICAgICByZXR1cm4gc2Vzc2lvblxuICAgIH0sXG4gICAgYXN5bmMgc2lnbkluKHsgdXNlciwgYWNjb3VudCwgcHJvZmlsZSB9KSB7XG4gICAgICAvLyBBbGxvdyBzaWduIGluIC0gdXNlciBjcmVhdGlvbiB3aWxsIGJlIGhhbmRsZWQgaW4gQVBJIGVuZHBvaW50cyBpZiBuZWVkZWRcbiAgICAgIHJldHVybiB0cnVlXG4gICAgfSxcbiAgICBhc3luYyBqd3QoeyB0b2tlbiwgdXNlciB9KSB7XG4gICAgICBpZiAodXNlcikge1xuICAgICAgICB0b2tlbi5yb2xlID0gKHVzZXIgYXMgYW55KS5yb2xlXG4gICAgICB9XG4gICAgICByZXR1cm4gdG9rZW5cbiAgICB9LFxuICB9LFxuICBwYWdlczoge1xuICAgIHNpZ25JbjogXCIvYXV0aC9zaWduaW5cIixcbiAgICBlcnJvcjogXCIvYXV0aC9lcnJvclwiLFxuICB9LFxuICBzZXNzaW9uOiB7XG4gICAgc3RyYXRlZ3k6IFwiZGF0YWJhc2VcIixcbiAgfSxcbiAgZXZlbnRzOiB7XG4gICAgYXN5bmMgc2lnbkluKHsgdXNlciwgYWNjb3VudCwgcHJvZmlsZSB9KSB7XG4gICAgICBjb25zb2xlLmxvZyhcIlVzZXIgc2lnbmVkIGluOlwiLCB7IHVzZXI6IHVzZXIuZW1haWwsIHByb3ZpZGVyOiBhY2NvdW50Py5wcm92aWRlciB9KVxuICAgIH0sXG4gICAgYXN5bmMgc2lnbk91dCh7IHNlc3Npb24gfSkge1xuICAgICAgY29uc29sZS5sb2coXCJVc2VyIHNpZ25lZCBvdXQ6XCIsIHNlc3Npb24/LnVzZXI/LmVtYWlsKVxuICAgIH0sXG4gIH0sXG59Il0sIm5hbWVzIjpbIlByaXNtYUFkYXB0ZXIiLCJHb29nbGVQcm92aWRlciIsInByaXNtYSIsImF1dGhPcHRpb25zIiwiYWRhcHRlciIsInByb3ZpZGVycyIsImNsaWVudElkIiwicHJvY2VzcyIsImVudiIsIkdPT0dMRV9DTElFTlRfSUQiLCJjbGllbnRTZWNyZXQiLCJHT09HTEVfQ0xJRU5UX1NFQ1JFVCIsImNhbGxiYWNrcyIsInNlc3Npb24iLCJ1c2VyIiwiaWQiLCJyb2xlIiwic2lnbkluIiwiYWNjb3VudCIsInByb2ZpbGUiLCJqd3QiLCJ0b2tlbiIsInBhZ2VzIiwiZXJyb3IiLCJzdHJhdGVneSIsImV2ZW50cyIsImNvbnNvbGUiLCJsb2ciLCJlbWFpbCIsInByb3ZpZGVyIiwic2lnbk91dCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/credits.ts":
/*!****************************!*\
  !*** ./src/lib/credits.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CREDIT_CONFIG: () => (/* binding */ CREDIT_CONFIG),\n/* harmony export */   DEFAULT_CREDIT_PACKAGES: () => (/* binding */ DEFAULT_CREDIT_PACKAGES),\n/* harmony export */   calculateCreditRevenueSplit: () => (/* binding */ calculateCreditRevenueSplit),\n/* harmony export */   creditsToUSD: () => (/* binding */ creditsToUSD),\n/* harmony export */   formatCreditPrice: () => (/* binding */ formatCreditPrice),\n/* harmony export */   formatCredits: () => (/* binding */ formatCredits),\n/* harmony export */   getRecommendedCreditPrice: () => (/* binding */ getRecommendedCreditPrice),\n/* harmony export */   isValidCreditPrice: () => (/* binding */ isValidCreditPrice),\n/* harmony export */   usdToCredits: () => (/* binding */ usdToCredits),\n/* harmony export */   validateCreditPurchase: () => (/* binding */ validateCreditPurchase)\n/* harmony export */ });\n// Credit system configuration and utilities\nconst CREDIT_CONFIG = {\n    // Credit to USD conversion rate (1 credit = $0.10)\n    CREDIT_TO_USD_RATE: 0.10,\n    // Default credit prices for content\n    DEFAULT_CHAPTER_CREDITS: 5,\n    DEFAULT_NOVEL_CREDITS: 50,\n    // Revenue sharing for credit purchases\n    PLATFORM_PERCENTAGE: 30,\n    AUTHOR_PERCENTAGE: 70\n};\n// Default credit packages\nconst DEFAULT_CREDIT_PACKAGES = [\n    {\n        name: \"Starter Pack\",\n        description: \"Perfect for trying out premium content\",\n        credits: 50,\n        price: 4.99,\n        bonusCredits: 0,\n        sortOrder: 1\n    },\n    {\n        name: \"Value Pack\",\n        description: \"Great value for regular readers\",\n        credits: 120,\n        price: 9.99,\n        bonusCredits: 10,\n        sortOrder: 2\n    },\n    {\n        name: \"Premium Pack\",\n        description: \"Best value for avid readers\",\n        credits: 300,\n        price: 19.99,\n        bonusCredits: 50,\n        sortOrder: 3\n    },\n    {\n        name: \"Ultimate Pack\",\n        description: \"Maximum value for power readers\",\n        credits: 600,\n        price: 34.99,\n        bonusCredits: 150,\n        sortOrder: 4\n    }\n];\n// Helper functions\nconst formatCredits = (credits)=>{\n    return `${credits.toLocaleString()} credits`;\n};\nconst creditsToUSD = (credits)=>{\n    return credits * CREDIT_CONFIG.CREDIT_TO_USD_RATE;\n};\nconst usdToCredits = (usd)=>{\n    return Math.round(usd / CREDIT_CONFIG.CREDIT_TO_USD_RATE);\n};\nconst calculateCreditRevenueSplit = (credits)=>{\n    const dollarValue = creditsToUSD(credits);\n    const platformFee = dollarValue * CREDIT_CONFIG.PLATFORM_PERCENTAGE / 100;\n    const authorEarning = dollarValue - platformFee;\n    return {\n        dollarValue,\n        platformFee,\n        authorEarning\n    };\n};\n// Content pricing helpers\nconst getRecommendedCreditPrice = (contentType, wordCount)=>{\n    if (contentType === \"CHAPTER\") {\n        // Base price for chapters, can be adjusted based on word count\n        if (wordCount) {\n            // 1 credit per 500 words, minimum 3 credits\n            return Math.max(3, Math.ceil(wordCount / 500));\n        }\n        return CREDIT_CONFIG.DEFAULT_CHAPTER_CREDITS;\n    } else {\n        // Base price for novels, can be adjusted based on total word count or chapter count\n        if (wordCount) {\n            // 1 credit per 1000 words, minimum 20 credits\n            return Math.max(20, Math.ceil(wordCount / 1000));\n        }\n        return CREDIT_CONFIG.DEFAULT_NOVEL_CREDITS;\n    }\n};\nconst formatCreditPrice = (credits)=>{\n    const usdValue = creditsToUSD(credits);\n    return `${formatCredits(credits)} ($${usdValue.toFixed(2)})`;\n};\n// Validation helpers\nconst isValidCreditPrice = (credits)=>{\n    return credits > 0 && credits <= 1000 && Number.isInteger(credits);\n};\nconst validateCreditPurchase = (userBalance, requiredCredits)=>{\n    if (userBalance >= requiredCredits) {\n        return {\n            canPurchase: true\n        };\n    }\n    return {\n        canPurchase: false,\n        shortfall: requiredCredits - userBalance\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2NyZWRpdHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFBLDRDQUE0QztBQUVyQyxNQUFNQSxnQkFBZ0I7SUFDM0IsbURBQW1EO0lBQ25EQyxvQkFBb0I7SUFFcEIsb0NBQW9DO0lBQ3BDQyx5QkFBeUI7SUFDekJDLHVCQUF1QjtJQUV2Qix1Q0FBdUM7SUFDdkNDLHFCQUFxQjtJQUNyQkMsbUJBQW1CO0FBQ3JCLEVBQVU7QUFFViwwQkFBMEI7QUFDbkIsTUFBTUMsMEJBQTBCO0lBQ3JDO1FBQ0VDLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxTQUFTO1FBQ1RDLE9BQU87UUFDUEMsY0FBYztRQUNkQyxXQUFXO0lBQ2I7SUFDQTtRQUNFTCxNQUFNO1FBQ05DLGFBQWE7UUFDYkMsU0FBUztRQUNUQyxPQUFPO1FBQ1BDLGNBQWM7UUFDZEMsV0FBVztJQUNiO0lBQ0E7UUFDRUwsTUFBTTtRQUNOQyxhQUFhO1FBQ2JDLFNBQVM7UUFDVEMsT0FBTztRQUNQQyxjQUFjO1FBQ2RDLFdBQVc7SUFDYjtJQUNBO1FBQ0VMLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxTQUFTO1FBQ1RDLE9BQU87UUFDUEMsY0FBYztRQUNkQyxXQUFXO0lBQ2I7Q0FDRCxDQUFTO0FBRVYsbUJBQW1CO0FBQ1osTUFBTUMsZ0JBQWdCLENBQUNKO0lBQzVCLE9BQU8sQ0FBQyxFQUFFQSxRQUFRSyxjQUFjLEdBQUcsUUFBUSxDQUFDO0FBQzlDLEVBQUM7QUFFTSxNQUFNQyxlQUFlLENBQUNOO0lBQzNCLE9BQU9BLFVBQVVULGNBQWNDLGtCQUFrQjtBQUNuRCxFQUFDO0FBRU0sTUFBTWUsZUFBZSxDQUFDQztJQUMzQixPQUFPQyxLQUFLQyxLQUFLLENBQUNGLE1BQU1qQixjQUFjQyxrQkFBa0I7QUFDMUQsRUFBQztBQUVNLE1BQU1tQiw4QkFBOEIsQ0FBQ1g7SUFDMUMsTUFBTVksY0FBY04sYUFBYU47SUFDakMsTUFBTWEsY0FBYyxjQUFldEIsY0FBY0ksbUJBQW1CLEdBQUk7SUFDeEUsTUFBTW1CLGdCQUFnQkYsY0FBY0M7SUFFcEMsT0FBTztRQUNMRDtRQUNBQztRQUNBQztJQUNGO0FBQ0YsRUFBQztBQUVELDBCQUEwQjtBQUNuQixNQUFNQyw0QkFBNEIsQ0FBQ0MsYUFBa0NDO0lBQzFFLElBQUlELGdCQUFnQixXQUFXO1FBQzdCLCtEQUErRDtRQUMvRCxJQUFJQyxXQUFXO1lBQ2IsNENBQTRDO1lBQzVDLE9BQU9SLEtBQUtTLEdBQUcsQ0FBQyxHQUFHVCxLQUFLVSxJQUFJLENBQUNGLFlBQVk7UUFDM0M7UUFDQSxPQUFPMUIsY0FBY0UsdUJBQXVCO0lBQzlDLE9BQU87UUFDTCxvRkFBb0Y7UUFDcEYsSUFBSXdCLFdBQVc7WUFDYiw4Q0FBOEM7WUFDOUMsT0FBT1IsS0FBS1MsR0FBRyxDQUFDLElBQUlULEtBQUtVLElBQUksQ0FBQ0YsWUFBWTtRQUM1QztRQUNBLE9BQU8xQixjQUFjRyxxQkFBcUI7SUFDNUM7QUFDRixFQUFDO0FBRU0sTUFBTTBCLG9CQUFvQixDQUFDcEI7SUFDaEMsTUFBTXFCLFdBQVdmLGFBQWFOO0lBQzlCLE9BQU8sQ0FBQyxFQUFFSSxjQUFjSixTQUFTLEdBQUcsRUFBRXFCLFNBQVNDLE9BQU8sQ0FBQyxHQUFHLENBQUMsQ0FBQztBQUM5RCxFQUFDO0FBRUQscUJBQXFCO0FBQ2QsTUFBTUMscUJBQXFCLENBQUN2QjtJQUNqQyxPQUFPQSxVQUFVLEtBQUtBLFdBQVcsUUFBUXdCLE9BQU9DLFNBQVMsQ0FBQ3pCO0FBQzVELEVBQUM7QUFFTSxNQUFNMEIseUJBQXlCLENBQUNDLGFBQXFCQztJQUkxRCxJQUFJRCxlQUFlQyxpQkFBaUI7UUFDbEMsT0FBTztZQUFFQyxhQUFhO1FBQUs7SUFDN0I7SUFFQSxPQUFPO1FBQ0xBLGFBQWE7UUFDYkMsV0FBV0Ysa0JBQWtCRDtJQUMvQjtBQUNGLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ibGFjay1ibG9nLy4vc3JjL2xpYi9jcmVkaXRzLnRzPzE2YmMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ3JlZGl0IHN5c3RlbSBjb25maWd1cmF0aW9uIGFuZCB1dGlsaXRpZXNcblxuZXhwb3J0IGNvbnN0IENSRURJVF9DT05GSUcgPSB7XG4gIC8vIENyZWRpdCB0byBVU0QgY29udmVyc2lvbiByYXRlICgxIGNyZWRpdCA9ICQwLjEwKVxuICBDUkVESVRfVE9fVVNEX1JBVEU6IDAuMTAsXG4gIFxuICAvLyBEZWZhdWx0IGNyZWRpdCBwcmljZXMgZm9yIGNvbnRlbnRcbiAgREVGQVVMVF9DSEFQVEVSX0NSRURJVFM6IDUsICAvLyA1IGNyZWRpdHMgPSAkMC41MFxuICBERUZBVUxUX05PVkVMX0NSRURJVFM6IDUwLCAgIC8vIDUwIGNyZWRpdHMgPSAkNS4wMFxuICBcbiAgLy8gUmV2ZW51ZSBzaGFyaW5nIGZvciBjcmVkaXQgcHVyY2hhc2VzXG4gIFBMQVRGT1JNX1BFUkNFTlRBR0U6IDMwLFxuICBBVVRIT1JfUEVSQ0VOVEFHRTogNzAsXG59IGFzIGNvbnN0XG5cbi8vIERlZmF1bHQgY3JlZGl0IHBhY2thZ2VzXG5leHBvcnQgY29uc3QgREVGQVVMVF9DUkVESVRfUEFDS0FHRVMgPSBbXG4gIHtcbiAgICBuYW1lOiBcIlN0YXJ0ZXIgUGFja1wiLFxuICAgIGRlc2NyaXB0aW9uOiBcIlBlcmZlY3QgZm9yIHRyeWluZyBvdXQgcHJlbWl1bSBjb250ZW50XCIsXG4gICAgY3JlZGl0czogNTAsXG4gICAgcHJpY2U6IDQuOTksXG4gICAgYm9udXNDcmVkaXRzOiAwLFxuICAgIHNvcnRPcmRlcjogMSxcbiAgfSxcbiAge1xuICAgIG5hbWU6IFwiVmFsdWUgUGFja1wiLFxuICAgIGRlc2NyaXB0aW9uOiBcIkdyZWF0IHZhbHVlIGZvciByZWd1bGFyIHJlYWRlcnNcIixcbiAgICBjcmVkaXRzOiAxMjAsXG4gICAgcHJpY2U6IDkuOTksXG4gICAgYm9udXNDcmVkaXRzOiAxMCwgLy8gMTAlIGJvbnVzXG4gICAgc29ydE9yZGVyOiAyLFxuICB9LFxuICB7XG4gICAgbmFtZTogXCJQcmVtaXVtIFBhY2tcIixcbiAgICBkZXNjcmlwdGlvbjogXCJCZXN0IHZhbHVlIGZvciBhdmlkIHJlYWRlcnNcIixcbiAgICBjcmVkaXRzOiAzMDAsXG4gICAgcHJpY2U6IDE5Ljk5LFxuICAgIGJvbnVzQ3JlZGl0czogNTAsIC8vIDE2JSBib251c1xuICAgIHNvcnRPcmRlcjogMyxcbiAgfSxcbiAge1xuICAgIG5hbWU6IFwiVWx0aW1hdGUgUGFja1wiLFxuICAgIGRlc2NyaXB0aW9uOiBcIk1heGltdW0gdmFsdWUgZm9yIHBvd2VyIHJlYWRlcnNcIixcbiAgICBjcmVkaXRzOiA2MDAsXG4gICAgcHJpY2U6IDM0Ljk5LFxuICAgIGJvbnVzQ3JlZGl0czogMTUwLCAvLyAyNSUgYm9udXNcbiAgICBzb3J0T3JkZXI6IDQsXG4gIH0sXG5dIGFzIGNvbnN0XG5cbi8vIEhlbHBlciBmdW5jdGlvbnNcbmV4cG9ydCBjb25zdCBmb3JtYXRDcmVkaXRzID0gKGNyZWRpdHM6IG51bWJlcik6IHN0cmluZyA9PiB7XG4gIHJldHVybiBgJHtjcmVkaXRzLnRvTG9jYWxlU3RyaW5nKCl9IGNyZWRpdHNgXG59XG5cbmV4cG9ydCBjb25zdCBjcmVkaXRzVG9VU0QgPSAoY3JlZGl0czogbnVtYmVyKTogbnVtYmVyID0+IHtcbiAgcmV0dXJuIGNyZWRpdHMgKiBDUkVESVRfQ09ORklHLkNSRURJVF9UT19VU0RfUkFURVxufVxuXG5leHBvcnQgY29uc3QgdXNkVG9DcmVkaXRzID0gKHVzZDogbnVtYmVyKTogbnVtYmVyID0+IHtcbiAgcmV0dXJuIE1hdGgucm91bmQodXNkIC8gQ1JFRElUX0NPTkZJRy5DUkVESVRfVE9fVVNEX1JBVEUpXG59XG5cbmV4cG9ydCBjb25zdCBjYWxjdWxhdGVDcmVkaXRSZXZlbnVlU3BsaXQgPSAoY3JlZGl0czogbnVtYmVyKSA9PiB7XG4gIGNvbnN0IGRvbGxhclZhbHVlID0gY3JlZGl0c1RvVVNEKGNyZWRpdHMpXG4gIGNvbnN0IHBsYXRmb3JtRmVlID0gKGRvbGxhclZhbHVlICogQ1JFRElUX0NPTkZJRy5QTEFURk9STV9QRVJDRU5UQUdFKSAvIDEwMFxuICBjb25zdCBhdXRob3JFYXJuaW5nID0gZG9sbGFyVmFsdWUgLSBwbGF0Zm9ybUZlZVxuICBcbiAgcmV0dXJuIHtcbiAgICBkb2xsYXJWYWx1ZSxcbiAgICBwbGF0Zm9ybUZlZSxcbiAgICBhdXRob3JFYXJuaW5nLFxuICB9XG59XG5cbi8vIENvbnRlbnQgcHJpY2luZyBoZWxwZXJzXG5leHBvcnQgY29uc3QgZ2V0UmVjb21tZW5kZWRDcmVkaXRQcmljZSA9IChjb250ZW50VHlwZTogJ05PVkVMJyB8ICdDSEFQVEVSJywgd29yZENvdW50PzogbnVtYmVyKTogbnVtYmVyID0+IHtcbiAgaWYgKGNvbnRlbnRUeXBlID09PSAnQ0hBUFRFUicpIHtcbiAgICAvLyBCYXNlIHByaWNlIGZvciBjaGFwdGVycywgY2FuIGJlIGFkanVzdGVkIGJhc2VkIG9uIHdvcmQgY291bnRcbiAgICBpZiAod29yZENvdW50KSB7XG4gICAgICAvLyAxIGNyZWRpdCBwZXIgNTAwIHdvcmRzLCBtaW5pbXVtIDMgY3JlZGl0c1xuICAgICAgcmV0dXJuIE1hdGgubWF4KDMsIE1hdGguY2VpbCh3b3JkQ291bnQgLyA1MDApKVxuICAgIH1cbiAgICByZXR1cm4gQ1JFRElUX0NPTkZJRy5ERUZBVUxUX0NIQVBURVJfQ1JFRElUU1xuICB9IGVsc2Uge1xuICAgIC8vIEJhc2UgcHJpY2UgZm9yIG5vdmVscywgY2FuIGJlIGFkanVzdGVkIGJhc2VkIG9uIHRvdGFsIHdvcmQgY291bnQgb3IgY2hhcHRlciBjb3VudFxuICAgIGlmICh3b3JkQ291bnQpIHtcbiAgICAgIC8vIDEgY3JlZGl0IHBlciAxMDAwIHdvcmRzLCBtaW5pbXVtIDIwIGNyZWRpdHNcbiAgICAgIHJldHVybiBNYXRoLm1heCgyMCwgTWF0aC5jZWlsKHdvcmRDb3VudCAvIDEwMDApKVxuICAgIH1cbiAgICByZXR1cm4gQ1JFRElUX0NPTkZJRy5ERUZBVUxUX05PVkVMX0NSRURJVFNcbiAgfVxufVxuXG5leHBvcnQgY29uc3QgZm9ybWF0Q3JlZGl0UHJpY2UgPSAoY3JlZGl0czogbnVtYmVyKTogc3RyaW5nID0+IHtcbiAgY29uc3QgdXNkVmFsdWUgPSBjcmVkaXRzVG9VU0QoY3JlZGl0cylcbiAgcmV0dXJuIGAke2Zvcm1hdENyZWRpdHMoY3JlZGl0cyl9ICgkJHt1c2RWYWx1ZS50b0ZpeGVkKDIpfSlgXG59XG5cbi8vIFZhbGlkYXRpb24gaGVscGVyc1xuZXhwb3J0IGNvbnN0IGlzVmFsaWRDcmVkaXRQcmljZSA9IChjcmVkaXRzOiBudW1iZXIpOiBib29sZWFuID0+IHtcbiAgcmV0dXJuIGNyZWRpdHMgPiAwICYmIGNyZWRpdHMgPD0gMTAwMCAmJiBOdW1iZXIuaXNJbnRlZ2VyKGNyZWRpdHMpXG59XG5cbmV4cG9ydCBjb25zdCB2YWxpZGF0ZUNyZWRpdFB1cmNoYXNlID0gKHVzZXJCYWxhbmNlOiBudW1iZXIsIHJlcXVpcmVkQ3JlZGl0czogbnVtYmVyKToge1xuICBjYW5QdXJjaGFzZTogYm9vbGVhblxuICBzaG9ydGZhbGw/OiBudW1iZXJcbn0gPT4ge1xuICBpZiAodXNlckJhbGFuY2UgPj0gcmVxdWlyZWRDcmVkaXRzKSB7XG4gICAgcmV0dXJuIHsgY2FuUHVyY2hhc2U6IHRydWUgfVxuICB9XG4gIFxuICByZXR1cm4ge1xuICAgIGNhblB1cmNoYXNlOiBmYWxzZSxcbiAgICBzaG9ydGZhbGw6IHJlcXVpcmVkQ3JlZGl0cyAtIHVzZXJCYWxhbmNlXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJDUkVESVRfQ09ORklHIiwiQ1JFRElUX1RPX1VTRF9SQVRFIiwiREVGQVVMVF9DSEFQVEVSX0NSRURJVFMiLCJERUZBVUxUX05PVkVMX0NSRURJVFMiLCJQTEFURk9STV9QRVJDRU5UQUdFIiwiQVVUSE9SX1BFUkNFTlRBR0UiLCJERUZBVUxUX0NSRURJVF9QQUNLQUdFUyIsIm5hbWUiLCJkZXNjcmlwdGlvbiIsImNyZWRpdHMiLCJwcmljZSIsImJvbnVzQ3JlZGl0cyIsInNvcnRPcmRlciIsImZvcm1hdENyZWRpdHMiLCJ0b0xvY2FsZVN0cmluZyIsImNyZWRpdHNUb1VTRCIsInVzZFRvQ3JlZGl0cyIsInVzZCIsIk1hdGgiLCJyb3VuZCIsImNhbGN1bGF0ZUNyZWRpdFJldmVudWVTcGxpdCIsImRvbGxhclZhbHVlIiwicGxhdGZvcm1GZWUiLCJhdXRob3JFYXJuaW5nIiwiZ2V0UmVjb21tZW5kZWRDcmVkaXRQcmljZSIsImNvbnRlbnRUeXBlIiwid29yZENvdW50IiwibWF4IiwiY2VpbCIsImZvcm1hdENyZWRpdFByaWNlIiwidXNkVmFsdWUiLCJ0b0ZpeGVkIiwiaXNWYWxpZENyZWRpdFByaWNlIiwiTnVtYmVyIiwiaXNJbnRlZ2VyIiwidmFsaWRhdGVDcmVkaXRQdXJjaGFzZSIsInVzZXJCYWxhbmNlIiwicmVxdWlyZWRDcmVkaXRzIiwiY2FuUHVyY2hhc2UiLCJzaG9ydGZhbGwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/credits.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/credit-transaction-manager.ts":
/*!********************************************************!*\
  !*** ./src/lib/database/credit-transaction-manager.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreditTransactionManager: () => (/* binding */ CreditTransactionManager)\n/* harmony export */ });\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n\nclass CreditTransactionManager {\n    /**\n   * Execute credit purchase with full transaction safety\n   */ static async executeCreditPurchase(purchaseData) {\n        return await _lib_db__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(async (tx)=>{\n            // 1. Get current user balance with row-level locking\n            const user = await tx.user.findUnique({\n                where: {\n                    id: purchaseData.userId\n                },\n                select: {\n                    id: true,\n                    creditBalance: true,\n                    email: true,\n                    name: true\n                }\n            });\n            if (!user) {\n                throw new Error(\"User not found\");\n            }\n            // 2. Get package details\n            const creditPackage = await tx.creditPackage.findUnique({\n                where: {\n                    id: purchaseData.packageId\n                },\n                select: {\n                    id: true,\n                    name: true,\n                    description: true,\n                    credits: true,\n                    bonusCredits: true,\n                    price: true,\n                    currency: true,\n                    isActive: true\n                }\n            });\n            if (!creditPackage || !creditPackage.isActive) {\n                throw new Error(\"Credit package not found or inactive\");\n            }\n            // 3. Validate purchase data consistency\n            if (purchaseData.credits !== creditPackage.credits || purchaseData.bonusCredits !== creditPackage.bonusCredits || purchaseData.totalCredits !== creditPackage.credits + creditPackage.bonusCredits || purchaseData.amount !== creditPackage.price || purchaseData.currency !== creditPackage.currency) {\n                throw new Error(\"Purchase data inconsistent with package details\");\n            }\n            const currentBalance = user.creditBalance;\n            const newBalance = currentBalance + purchaseData.totalCredits;\n            // 4. Create credit purchase record\n            const creditPurchase = await tx.creditPurchase.create({\n                data: {\n                    userId: purchaseData.userId,\n                    packageId: purchaseData.packageId,\n                    credits: purchaseData.credits,\n                    bonusCredits: purchaseData.bonusCredits,\n                    totalCredits: purchaseData.totalCredits,\n                    amount: purchaseData.amount,\n                    currency: purchaseData.currency,\n                    stripePaymentId: purchaseData.stripePaymentId,\n                    status: purchaseData.status\n                },\n                include: {\n                    package: true,\n                    user: {\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true\n                        }\n                    }\n                }\n            });\n            // 5. Update user credit balance (only if payment is completed)\n            if (purchaseData.status === \"COMPLETED\") {\n                await tx.user.update({\n                    where: {\n                        id: purchaseData.userId\n                    },\n                    data: {\n                        creditBalance: newBalance\n                    }\n                });\n                // 6. Create credit transaction record\n                await tx.creditTransaction.create({\n                    data: {\n                        userId: purchaseData.userId,\n                        type: \"PURCHASE\",\n                        status: \"COMPLETED\",\n                        amount: purchaseData.totalCredits,\n                        description: `Credit purchase: ${creditPackage.name}`,\n                        sourceType: \"purchase\",\n                        sourceId: creditPurchase.id,\n                        balanceBefore: currentBalance,\n                        balanceAfter: newBalance,\n                        purchaseId: creditPurchase.id\n                    }\n                });\n            }\n            return {\n                purchase: creditPurchase,\n                newBalance: purchaseData.status === \"COMPLETED\" ? newBalance : currentBalance\n            };\n        }, {\n            maxWait: 10000,\n            timeout: 30000,\n            isolationLevel: \"Serializable\" // Highest isolation level for financial transactions\n        });\n    }\n    /**\n   * Execute credit spending with transaction safety\n   */ static async executeCreditSpending(userId, amount, description, sourceType, sourceId) {\n        return await _lib_db__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(async (tx)=>{\n            // 1. Get current user balance with row-level locking\n            const user = await tx.user.findUnique({\n                where: {\n                    id: userId\n                },\n                select: {\n                    id: true,\n                    creditBalance: true\n                }\n            });\n            if (!user) {\n                throw new Error(\"User not found\");\n            }\n            const currentBalance = user.creditBalance;\n            const newBalance = currentBalance - amount;\n            // 2. Check sufficient balance\n            if (newBalance < 0) {\n                throw new Error(\"Insufficient credit balance\");\n            }\n            // 3. Update user balance\n            await tx.user.update({\n                where: {\n                    id: userId\n                },\n                data: {\n                    creditBalance: newBalance\n                }\n            });\n            // 4. Create debit transaction record\n            const transaction = await tx.creditTransaction.create({\n                data: {\n                    userId,\n                    type: \"SPEND\",\n                    status: \"COMPLETED\",\n                    amount: -amount,\n                    description,\n                    sourceType,\n                    sourceId,\n                    balanceBefore: currentBalance,\n                    balanceAfter: newBalance\n                }\n            });\n            return {\n                transaction,\n                newBalance\n            };\n        }, {\n            maxWait: 5000,\n            timeout: 15000,\n            isolationLevel: \"Serializable\"\n        });\n    }\n    /**\n   * Execute credit refund with transaction safety\n   */ static async executeCreditRefund(userId, amount, description, sourceType, sourceId) {\n        return await _lib_db__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(async (tx)=>{\n            // 1. Get current user balance\n            const user = await tx.user.findUnique({\n                where: {\n                    id: userId\n                },\n                select: {\n                    id: true,\n                    creditBalance: true\n                }\n            });\n            if (!user) {\n                throw new Error(\"User not found\");\n            }\n            const currentBalance = user.creditBalance;\n            const newBalance = currentBalance + amount;\n            // 2. Update user balance\n            await tx.user.update({\n                where: {\n                    id: userId\n                },\n                data: {\n                    creditBalance: newBalance\n                }\n            });\n            // 3. Create credit transaction record\n            const transaction = await tx.creditTransaction.create({\n                data: {\n                    userId,\n                    type: \"PURCHASE\",\n                    status: \"COMPLETED\",\n                    amount,\n                    description,\n                    sourceType,\n                    sourceId,\n                    balanceBefore: currentBalance,\n                    balanceAfter: newBalance\n                }\n            });\n            return {\n                transaction,\n                newBalance\n            };\n        }, {\n            maxWait: 5000,\n            timeout: 15000,\n            isolationLevel: \"Serializable\"\n        });\n    }\n    /**\n   * Execute batch credit operations (for bulk operations)\n   */ static async executeBatchCreditOperations(operations) {\n        return await _lib_db__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(async (tx)=>{\n            const results = [];\n            for (const operation of operations){\n                // Get current balance\n                const user = await tx.user.findUnique({\n                    where: {\n                        id: operation.userId\n                    },\n                    select: {\n                        id: true,\n                        creditBalance: true\n                    }\n                });\n                if (!user) {\n                    throw new Error(`User ${operation.userId} not found`);\n                }\n                const currentBalance = user.creditBalance;\n                const transactionAmount = operation.type === \"debit\" ? -operation.amount : operation.amount;\n                const newBalance = currentBalance + transactionAmount;\n                // Validate sufficient balance for debits\n                if (operation.type === \"debit\" && newBalance < 0) {\n                    throw new Error(`Insufficient balance for user ${operation.userId}`);\n                }\n                // Update balance\n                await tx.user.update({\n                    where: {\n                        id: operation.userId\n                    },\n                    data: {\n                        creditBalance: newBalance\n                    }\n                });\n                // Create transaction record\n                const transaction = await tx.creditTransaction.create({\n                    data: {\n                        userId: operation.userId,\n                        type: operation.type === \"debit\" ? \"SPEND\" : \"PURCHASE\",\n                        status: \"COMPLETED\",\n                        amount: transactionAmount,\n                        description: operation.description,\n                        sourceType: operation.sourceType,\n                        sourceId: operation.sourceId,\n                        balanceBefore: currentBalance,\n                        balanceAfter: newBalance\n                    }\n                });\n                results.push({\n                    userId: operation.userId,\n                    newBalance,\n                    transaction\n                });\n            }\n            return results;\n        }, {\n            maxWait: 15000,\n            timeout: 60000,\n            isolationLevel: \"Serializable\"\n        });\n    }\n    /**\n   * Get user balance with optional lock\n   */ static async getUserBalance(userId, lock = false) {\n        if (lock) {\n            return await _lib_db__WEBPACK_IMPORTED_MODULE_0__.prisma.$transaction(async (tx)=>{\n                const user = await tx.user.findUnique({\n                    where: {\n                        id: userId\n                    },\n                    select: {\n                        creditBalance: true\n                    }\n                });\n                return user?.creditBalance || 0;\n            }, {\n                isolationLevel: \"Serializable\"\n            });\n        } else {\n            const user = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n                where: {\n                    id: userId\n                },\n                select: {\n                    creditBalance: true\n                }\n            });\n            return user?.creditBalance || 0;\n        }\n    }\n    /**\n   * Validate transaction integrity\n   */ static async validateTransactionIntegrity(userId) {\n        const user = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.prisma.user.findUnique({\n            where: {\n                id: userId\n            },\n            select: {\n                creditBalance: true\n            }\n        });\n        if (!user) {\n            throw new Error(\"User not found\");\n        }\n        // Calculate expected balance from transactions\n        const transactions = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.prisma.creditTransaction.findMany({\n            where: {\n                userId,\n                status: \"COMPLETED\"\n            },\n            orderBy: {\n                createdAt: \"asc\"\n            }\n        });\n        const expectedBalance = transactions.reduce((balance, transaction)=>{\n            return balance + transaction.amount;\n        }, 0);\n        const actualBalance = user.creditBalance;\n        const discrepancy = actualBalance - expectedBalance;\n        return {\n            isValid: Math.abs(discrepancy) < 0.01,\n            expectedBalance,\n            actualBalance,\n            discrepancy\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/credit-transaction-manager.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9zcmMvbGliL2RiLnRzPzllNGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/email/email-service.ts":
/*!****************************************!*\
  !*** ./src/lib/email/email-service.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailService: () => (/* binding */ EmailService),\n/* harmony export */   sendBulkEmail: () => (/* binding */ sendBulkEmail),\n/* harmony export */   sendEmail: () => (/* binding */ sendEmail),\n/* harmony export */   sendTemplateEmail: () => (/* binding */ sendTemplateEmail)\n/* harmony export */ });\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nodemailer */ \"(rsc)/./node_modules/nodemailer/lib/nodemailer.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n\n\nclass EmailService {\n    static{\n        this.provider = null;\n    }\n    /**\n   * Initialize email service with the configured provider\n   */ static async initialize() {\n        if (this.provider) return;\n        // Determine which email provider to use based on environment variables\n        if (process.env.SENDGRID_API_KEY) {\n            this.provider = await this.createSendGridProvider();\n        } else if (process.env.MAILGUN_API_KEY) {\n            this.provider = await this.createMailgunProvider();\n        } else if (process.env.AWS_SES_REGION) {\n            this.provider = await this.createSESProvider();\n        } else if (process.env.RESEND_API_KEY) {\n            this.provider = await this.createResendProvider();\n        } else if (process.env.SMTP_HOST) {\n            this.provider = await this.createSMTPProvider();\n        } else {\n            // Fallback to console logging for development\n            this.provider = this.createConsoleProvider();\n        }\n    }\n    /**\n   * Send an email using the configured provider\n   */ static async sendEmail(options) {\n        await this.initialize();\n        if (!this.provider) {\n            throw new Error(\"No email provider configured\");\n        }\n        try {\n            const result = await this.provider.send({\n                ...options,\n                from: options.from || process.env.EMAIL_FROM || \"Black Blogs <<EMAIL>>\"\n            });\n            // Log email to database for tracking\n            await this.logEmail(options, result);\n            return result;\n        } catch (error) {\n            console.error(\"Email sending failed:\", error);\n            // Log failed email attempt\n            await this.logEmail(options, {\n                messageId: \"\",\n                success: false\n            }, error);\n            throw error;\n        }\n    }\n    /**\n   * Send email using template\n   */ static async sendTemplateEmail(to, template, data = {}) {\n        // Simple template variable replacement\n        const processTemplate = (content)=>{\n            return content.replace(/\\{\\{(\\w+)\\}\\}/g, (match, key)=>{\n                return data[key] || match;\n            });\n        };\n        return this.sendEmail({\n            to,\n            subject: processTemplate(template.subject),\n            html: processTemplate(template.html),\n            text: processTemplate(template.text)\n        });\n    }\n    /**\n   * Create SendGrid provider\n   */ static async createSendGridProvider() {\n        const sgMail = __webpack_require__(/*! @sendgrid/mail */ \"(rsc)/./node_modules/@sendgrid/mail/index.js\");\n        sgMail.setApiKey(process.env.SENDGRID_API_KEY);\n        return {\n            name: \"SendGrid\",\n            async send (options) {\n                const msg = {\n                    to: options.to,\n                    from: options.from,\n                    subject: options.subject,\n                    html: options.html,\n                    text: options.text,\n                    replyTo: options.replyTo,\n                    attachments: options.attachments\n                };\n                const result = await sgMail.send(msg);\n                return {\n                    messageId: result[0].headers[\"x-message-id\"] || \"sendgrid-\" + Date.now(),\n                    success: true\n                };\n            }\n        };\n    }\n    /**\n   * Create Resend provider\n   */ static async createResendProvider() {\n        const { Resend } = __webpack_require__(/*! resend */ \"(rsc)/./node_modules/resend/dist/index.js\");\n        const resend = new Resend(process.env.RESEND_API_KEY);\n        return {\n            name: \"Resend\",\n            async send (options) {\n                const result = await resend.emails.send({\n                    from: options.from,\n                    to: options.to,\n                    subject: options.subject,\n                    html: options.html,\n                    text: options.text,\n                    reply_to: options.replyTo,\n                    attachments: options.attachments\n                });\n                return {\n                    messageId: result.data?.id || \"resend-\" + Date.now(),\n                    success: !result.error\n                };\n            }\n        };\n    }\n    /**\n   * Create SMTP provider using nodemailer\n   */ static async createSMTPProvider() {\n        const transporter = nodemailer__WEBPACK_IMPORTED_MODULE_0__.createTransporter({\n            host: process.env.SMTP_HOST,\n            port: parseInt(process.env.SMTP_PORT || \"587\"),\n            secure: process.env.SMTP_SECURE === \"true\",\n            auth: {\n                user: process.env.SMTP_USER,\n                pass: process.env.SMTP_PASS\n            }\n        });\n        return {\n            name: \"SMTP\",\n            async send (options) {\n                const result = await transporter.sendMail({\n                    from: options.from,\n                    to: options.to,\n                    subject: options.subject,\n                    html: options.html,\n                    text: options.text,\n                    replyTo: options.replyTo,\n                    attachments: options.attachments\n                });\n                return {\n                    messageId: result.messageId,\n                    success: true\n                };\n            }\n        };\n    }\n    /**\n   * Create Mailgun provider\n   */ static async createMailgunProvider() {\n        const formData = __webpack_require__(/*! form-data */ \"(rsc)/./node_modules/form-data/lib/form_data.js\");\n        const Mailgun = __webpack_require__(/*! mailgun.js */ \"(rsc)/./node_modules/mailgun.js/CJS/mailgun.node.cjs\");\n        const mailgun = new Mailgun(formData);\n        const mg = mailgun.client({\n            username: \"api\",\n            key: process.env.MAILGUN_API_KEY,\n            url: process.env.MAILGUN_URL || \"https://api.mailgun.net\"\n        });\n        return {\n            name: \"Mailgun\",\n            async send (options) {\n                const result = await mg.messages.create(process.env.MAILGUN_DOMAIN, {\n                    from: options.from,\n                    to: options.to,\n                    subject: options.subject,\n                    html: options.html,\n                    text: options.text,\n                    \"h:Reply-To\": options.replyTo,\n                    attachment: options.attachments\n                });\n                return {\n                    messageId: result.id,\n                    success: true\n                };\n            }\n        };\n    }\n    /**\n   * Create AWS SES provider\n   */ static async createSESProvider() {\n        const { SESClient, SendEmailCommand } = __webpack_require__(/*! @aws-sdk/client-ses */ \"(rsc)/./node_modules/@aws-sdk/client-ses/dist-es/index.js\");\n        const client = new SESClient({\n            region: process.env.AWS_SES_REGION,\n            credentials: {\n                accessKeyId: process.env.AWS_ACCESS_KEY_ID,\n                secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY\n            }\n        });\n        return {\n            name: \"AWS SES\",\n            async send (options) {\n                const command = new SendEmailCommand({\n                    Source: options.from,\n                    Destination: {\n                        ToAddresses: Array.isArray(options.to) ? options.to : [\n                            options.to\n                        ]\n                    },\n                    Message: {\n                        Subject: {\n                            Data: options.subject\n                        },\n                        Body: {\n                            Html: {\n                                Data: options.html\n                            },\n                            Text: {\n                                Data: options.text || \"\"\n                            }\n                        }\n                    },\n                    ReplyToAddresses: options.replyTo ? [\n                        options.replyTo\n                    ] : undefined\n                });\n                const result = await client.send(command);\n                return {\n                    messageId: result.MessageId,\n                    success: true\n                };\n            }\n        };\n    }\n    /**\n   * Create console provider for development\n   */ static createConsoleProvider() {\n        return {\n            name: \"Console\",\n            async send (options) {\n                console.log(\"\\n\\uD83D\\uDCE7 Email would be sent:\");\n                console.log(\"To:\", options.to);\n                console.log(\"Subject:\", options.subject);\n                console.log(\"From:\", options.from);\n                if (options.text) {\n                    console.log(\"\\nText Content:\");\n                    console.log(options.text);\n                }\n                console.log(\"\\nHTML Content:\");\n                console.log(options.html);\n                console.log(\"\\n\" + \"=\".repeat(50));\n                return {\n                    messageId: \"console-\" + Date.now(),\n                    success: true\n                };\n            }\n        };\n    }\n    /**\n   * Log email to database for tracking\n   */ static async logEmail(options, result, error) {\n        try {\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.emailLog.create({\n                data: {\n                    to: Array.isArray(options.to) ? options.to.join(\",\") : options.to,\n                    subject: options.subject,\n                    provider: this.provider?.name || \"Unknown\",\n                    messageId: result.messageId,\n                    success: result.success,\n                    error: error ? JSON.stringify(error) : null,\n                    sentAt: new Date()\n                }\n            }).catch(()=>{\n                // Ignore database errors for email logging\n                console.log(\"Email logged (no DB model available)\");\n            });\n        } catch (error) {\n            // Don't throw - email logging failures shouldn't break email sending\n            console.error(\"Failed to log email:\", error);\n        }\n    }\n    /**\n   * Get email sending statistics\n   */ static async getEmailStats(days = 30) {\n        try {\n            const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);\n            const stats = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.emailLog.groupBy({\n                by: [\n                    \"provider\",\n                    \"success\"\n                ],\n                where: {\n                    sentAt: {\n                        gte: startDate\n                    }\n                },\n                _count: true\n            });\n            const totalSent = stats.reduce((sum, stat)=>sum + stat._count, 0);\n            const successful = stats.filter((stat)=>stat.success).reduce((sum, stat)=>sum + stat._count, 0);\n            const byProvider = stats.reduce((acc, stat)=>{\n                acc[stat.provider] = (acc[stat.provider] || 0) + stat._count;\n                return acc;\n            }, {});\n            return {\n                totalSent,\n                successRate: totalSent > 0 ? successful / totalSent * 100 : 0,\n                failureRate: totalSent > 0 ? (totalSent - successful) / totalSent * 100 : 0,\n                byProvider\n            };\n        } catch (error) {\n            console.error(\"Failed to get email stats:\", error);\n            return {\n                totalSent: 0,\n                successRate: 0,\n                failureRate: 0,\n                byProvider: {}\n            };\n        }\n    }\n}\n// Export default instance for convenience\nconst emailService = new EmailService();\nconst sendEmail = (options)=>emailService.sendEmail(options);\nconst sendBulkEmail = (emails)=>emailService.sendBulkEmail(emails);\nconst sendTemplateEmail = (template, to, data)=>emailService.sendTemplateEmail(template, to, data);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/email/email-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/notifications/credit-purchase-notifications.ts":
/*!****************************************************************!*\
  !*** ./src/lib/notifications/credit-purchase-notifications.ts ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreditPurchaseNotificationService: () => (/* binding */ CreditPurchaseNotificationService)\n/* harmony export */ });\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _lib_credits__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/credits */ \"(rsc)/./src/lib/credits.ts\");\n/* harmony import */ var _lib_email_email_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/email/email-service */ \"(rsc)/./src/lib/email/email-service.ts\");\n\n\n\nclass CreditPurchaseNotificationService {\n    /**\n   * Send purchase confirmation email\n   */ static async sendPurchaseConfirmation(data) {\n        try {\n            const emailContent = this.generatePurchaseConfirmationEmail(data);\n            // Send email using the email service\n            const result = await _lib_email_email_service__WEBPACK_IMPORTED_MODULE_2__.EmailService.sendEmail({\n                to: data.userEmail,\n                subject: `Credit Purchase Confirmation - ${(0,_lib_credits__WEBPACK_IMPORTED_MODULE_1__.formatCredits)(data.totalCredits)}`,\n                html: emailContent,\n                text: this.generatePurchaseConfirmationText(data)\n            });\n            // Store email notification record\n            await _lib_db__WEBPACK_IMPORTED_MODULE_0__.prisma.emailNotification.create({\n                data: {\n                    type: \"CREDIT_PURCHASE_CONFIRMATION\",\n                    recipient: data.userEmail,\n                    subject: `Credit Purchase Confirmation - ${(0,_lib_credits__WEBPACK_IMPORTED_MODULE_1__.formatCredits)(data.totalCredits)}`,\n                    content: emailContent,\n                    status: result.success ? \"SENT\" : \"FAILED\",\n                    messageId: result.messageId,\n                    metadata: {\n                        purchaseId: data.purchaseId,\n                        credits: data.totalCredits,\n                        amount: data.amount\n                    }\n                }\n            }).catch((error)=>{\n                // Handle case where EmailNotification model doesn't exist yet\n                console.log(\"Email notification logged (no DB model):\", error.message);\n            });\n        } catch (error) {\n            console.error(\"Error sending purchase confirmation email:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Send low balance warning email\n   */ static async sendLowBalanceWarning(data) {\n        try {\n            const emailContent = this.generateLowBalanceWarningEmail(data);\n            // Send email using the email service\n            const result = await _lib_email_email_service__WEBPACK_IMPORTED_MODULE_2__.EmailService.sendEmail({\n                to: data.userEmail,\n                subject: \"Your Credit Balance is Running Low\",\n                html: emailContent,\n                text: this.generateLowBalanceWarningText(data)\n            });\n            // Store email notification record\n            await _lib_db__WEBPACK_IMPORTED_MODULE_0__.prisma.emailNotification.create({\n                data: {\n                    type: \"LOW_BALANCE_WARNING\",\n                    recipient: data.userEmail,\n                    subject: \"Your Credit Balance is Running Low\",\n                    content: emailContent,\n                    status: result.success ? \"SENT\" : \"FAILED\",\n                    messageId: result.messageId,\n                    metadata: {\n                        currentBalance: data.currentBalance,\n                        recommendedPackage: data.recommendedPackage?.name\n                    }\n                }\n            }).catch((error)=>{\n                console.log(\"Email notification logged (no DB model):\", error.message);\n            });\n        } catch (error) {\n            console.error(\"Error sending low balance warning email:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Send purchase receipt email\n   */ static async sendPurchaseReceipt(purchaseId) {\n        try {\n            const purchase = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.prisma.creditPurchase.findUnique({\n                where: {\n                    id: purchaseId\n                },\n                include: {\n                    user: true,\n                    package: true\n                }\n            });\n            if (!purchase || !purchase.user) {\n                throw new Error(\"Purchase or user not found\");\n            }\n            const emailContent = this.generatePurchaseReceiptEmail({\n                purchaseId: purchase.id,\n                userEmail: purchase.user.email,\n                userName: purchase.user.name || \"Valued Customer\",\n                packageName: purchase.package.name,\n                credits: purchase.credits,\n                bonusCredits: purchase.bonusCredits,\n                totalCredits: purchase.totalCredits,\n                amount: purchase.amount,\n                currency: purchase.currency,\n                purchaseDate: purchase.createdAt,\n                stripePaymentId: purchase.stripePaymentId || undefined\n            });\n            console.log(\"Purchase Receipt Email:\", {\n                to: purchase.user.email,\n                subject: `Receipt for Credit Purchase #${purchase.id.slice(-8)}`,\n                content: emailContent\n            });\n        } catch (error) {\n            console.error(\"Error sending purchase receipt email:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Generate purchase confirmation email text\n   */ static generatePurchaseConfirmationText(data) {\n        return `\nCredit Purchase Confirmation\n\nHi ${data.userName}!\n\nThank you for your credit purchase! Your credits have been added to your account and are ready to use.\n\nPurchase Details:\n- Package: ${data.packageName}\n- Credits: ${(0,_lib_credits__WEBPACK_IMPORTED_MODULE_1__.formatCredits)(data.credits)}\n${data.bonusCredits > 0 ? `- Bonus Credits: +${(0,_lib_credits__WEBPACK_IMPORTED_MODULE_1__.formatCredits)(data.bonusCredits)}` : \"\"}\n- Total Credits: ${(0,_lib_credits__WEBPACK_IMPORTED_MODULE_1__.formatCredits)(data.totalCredits)}\n- Amount Paid: $${data.amount.toFixed(2)} ${data.currency.toUpperCase()}\n- Purchase Date: ${data.purchaseDate.toLocaleDateString()}\n- Transaction ID: ${data.purchaseId}\n\nYour credits are now available in your account. Start exploring premium content and support your favorite authors!\n\nView My Credits: ${process.env.NEXTAUTH_URL}/dashboard/credits\n\nQuestions? Contact <NAME_EMAIL>\n\n© ${new Date().getFullYear()} Black Blogs. All rights reserved.\n    `.trim();\n    }\n    /**\n   * Generate low balance warning email text\n   */ static generateLowBalanceWarningText(data) {\n        return `\nLow Credit Balance Warning\n\nHi ${data.userName}!\n\nYour credit balance is running low!\n\nCurrent Balance: ${(0,_lib_credits__WEBPACK_IMPORTED_MODULE_1__.formatCredits)(data.currentBalance)}\n\nDon't let a low balance interrupt your reading experience. Top up your credits now to continue enjoying premium content.\n\n${data.recommendedPackage ? `\nRecommended for You:\n${data.recommendedPackage.name} - ${(0,_lib_credits__WEBPACK_IMPORTED_MODULE_1__.formatCredits)(data.recommendedPackage.credits)} for $${data.recommendedPackage.price}\nPerfect for continuing your reading journey!\n` : \"\"}\n\nTop Up Credits: ${process.env.NEXTAUTH_URL}/dashboard/credits\n\nYou can manage your notification preferences in your account settings.\n\n© ${new Date().getFullYear()} Black Blogs. All rights reserved.\n    `.trim();\n    }\n    /**\n   * Generate purchase confirmation email HTML\n   */ static generatePurchaseConfirmationEmail(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <title>Credit Purchase Confirmation</title>\n        <style>\n          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n          .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n          .header { background: #4F46E5; color: white; padding: 20px; text-align: center; }\n          .content { padding: 20px; background: #f9f9f9; }\n          .highlight { background: #EEF2FF; padding: 15px; border-radius: 8px; margin: 15px 0; }\n          .credits { font-size: 24px; font-weight: bold; color: #4F46E5; }\n          .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }\n        </style>\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <h1>🎉 Credit Purchase Confirmed!</h1>\n          </div>\n          \n          <div class=\"content\">\n            <h2>Hi ${data.userName}!</h2>\n            \n            <p>Thank you for your credit purchase! Your credits have been added to your account and are ready to use.</p>\n            \n            <div class=\"highlight\">\n              <h3>Purchase Details</h3>\n              <p><strong>Package:</strong> ${data.packageName}</p>\n              <p><strong>Credits:</strong> <span class=\"credits\">${(0,_lib_credits__WEBPACK_IMPORTED_MODULE_1__.formatCredits)(data.credits)}</span></p>\n              ${data.bonusCredits > 0 ? `<p><strong>Bonus Credits:</strong> <span class=\"credits\">+${(0,_lib_credits__WEBPACK_IMPORTED_MODULE_1__.formatCredits)(data.bonusCredits)}</span></p>` : \"\"}\n              <p><strong>Total Credits:</strong> <span class=\"credits\">${(0,_lib_credits__WEBPACK_IMPORTED_MODULE_1__.formatCredits)(data.totalCredits)}</span></p>\n              <p><strong>Amount Paid:</strong> $${data.amount.toFixed(2)} ${data.currency.toUpperCase()}</p>\n              <p><strong>Purchase Date:</strong> ${data.purchaseDate.toLocaleDateString()}</p>\n              <p><strong>Transaction ID:</strong> ${data.purchaseId}</p>\n            </div>\n            \n            <p>Your credits are now available in your account. Start exploring premium content and support your favorite authors!</p>\n            \n            <p style=\"text-align: center;\">\n              <a href=\"${process.env.NEXTAUTH_URL}/dashboard/credits\" \n                 style=\"background: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;\">\n                View My Credits\n              </a>\n            </p>\n          </div>\n          \n          <div class=\"footer\">\n            <p>Questions? Contact <NAME_EMAIL></p>\n            <p>© ${new Date().getFullYear()} Black Blogs. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate low balance warning email HTML\n   */ static generateLowBalanceWarningEmail(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <title>Low Credit Balance Warning</title>\n        <style>\n          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n          .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n          .header { background: #F59E0B; color: white; padding: 20px; text-align: center; }\n          .content { padding: 20px; background: #f9f9f9; }\n          .warning { background: #FEF3C7; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #F59E0B; }\n          .recommendation { background: #EEF2FF; padding: 15px; border-radius: 8px; margin: 15px 0; }\n          .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }\n        </style>\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <h1>⚠️ Low Credit Balance</h1>\n          </div>\n          \n          <div class=\"content\">\n            <h2>Hi ${data.userName}!</h2>\n            \n            <div class=\"warning\">\n              <p><strong>Your credit balance is running low!</strong></p>\n              <p>Current Balance: <strong>${(0,_lib_credits__WEBPACK_IMPORTED_MODULE_1__.formatCredits)(data.currentBalance)}</strong></p>\n            </div>\n            \n            <p>Don't let a low balance interrupt your reading experience. Top up your credits now to continue enjoying premium content.</p>\n            \n            ${data.recommendedPackage ? `\n              <div class=\"recommendation\">\n                <h3>💡 Recommended for You</h3>\n                <p><strong>${data.recommendedPackage.name}</strong></p>\n                <p>${(0,_lib_credits__WEBPACK_IMPORTED_MODULE_1__.formatCredits)(data.recommendedPackage.credits)} for $${data.recommendedPackage.price}</p>\n                <p>Perfect for continuing your reading journey!</p>\n              </div>\n            ` : \"\"}\n            \n            <p style=\"text-align: center;\">\n              <a href=\"${process.env.NEXTAUTH_URL}/dashboard/credits\" \n                 style=\"background: #4F46E5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;\">\n                Top Up Credits\n              </a>\n            </p>\n          </div>\n          \n          <div class=\"footer\">\n            <p>You can manage your notification preferences in your account settings.</p>\n            <p>© ${new Date().getFullYear()} Black Blogs. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n    /**\n   * Generate purchase receipt email HTML\n   */ static generatePurchaseReceiptEmail(data) {\n        return `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <title>Purchase Receipt</title>\n        <style>\n          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n          .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n          .header { background: #1F2937; color: white; padding: 20px; text-align: center; }\n          .content { padding: 20px; background: #f9f9f9; }\n          .receipt { background: white; padding: 20px; border-radius: 8px; margin: 15px 0; }\n          .receipt-header { border-bottom: 2px solid #E5E7EB; padding-bottom: 15px; margin-bottom: 15px; }\n          .receipt-row { display: flex; justify-content: space-between; margin: 10px 0; }\n          .total-row { border-top: 2px solid #E5E7EB; padding-top: 15px; margin-top: 15px; font-weight: bold; }\n          .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }\n        </style>\n      </head>\n      <body>\n        <div class=\"container\">\n          <div class=\"header\">\n            <h1>📧 Purchase Receipt</h1>\n          </div>\n          \n          <div class=\"content\">\n            <div class=\"receipt\">\n              <div class=\"receipt-header\">\n                <h2>Black Blogs</h2>\n                <p>Receipt #${data.purchaseId.slice(-8)}</p>\n                <p>Date: ${data.purchaseDate.toLocaleDateString()}</p>\n              </div>\n              \n              <h3>Bill To:</h3>\n              <p>${data.userName}<br>${data.userEmail}</p>\n              \n              <h3>Items:</h3>\n              <div class=\"receipt-row\">\n                <span>${data.packageName}</span>\n                <span>$${data.amount.toFixed(2)}</span>\n              </div>\n              <div class=\"receipt-row\">\n                <span style=\"margin-left: 20px; color: #666;\">Credits: ${(0,_lib_credits__WEBPACK_IMPORTED_MODULE_1__.formatCredits)(data.credits)}</span>\n                <span></span>\n              </div>\n              ${data.bonusCredits > 0 ? `\n                <div class=\"receipt-row\">\n                  <span style=\"margin-left: 20px; color: #10B981;\">Bonus Credits: ${(0,_lib_credits__WEBPACK_IMPORTED_MODULE_1__.formatCredits)(data.bonusCredits)}</span>\n                  <span style=\"color: #10B981;\">FREE</span>\n                </div>\n              ` : \"\"}\n              \n              <div class=\"receipt-row total-row\">\n                <span>Total</span>\n                <span>$${data.amount.toFixed(2)} ${data.currency.toUpperCase()}</span>\n              </div>\n              \n              <div class=\"receipt-row\">\n                <span>Total Credits Received</span>\n                <span>${(0,_lib_credits__WEBPACK_IMPORTED_MODULE_1__.formatCredits)(data.totalCredits)}</span>\n              </div>\n              \n              ${data.stripePaymentId ? `\n                <p style=\"margin-top: 20px; font-size: 12px; color: #666;\">\n                  Payment ID: ${data.stripePaymentId}\n                </p>\n              ` : \"\"}\n            </div>\n            \n            <p>Thank you for your purchase! Your credits are now available in your account.</p>\n          </div>\n          \n          <div class=\"footer\">\n            <p>Keep this receipt for your records.</p>\n            <p>© ${new Date().getFullYear()} Black Blogs. All rights reserved.</p>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/notifications/credit-purchase-notifications.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/payment/credit-payment-service.ts":
/*!***************************************************!*\
  !*** ./src/lib/payment/credit-payment-service.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreditPaymentService: () => (/* binding */ CreditPaymentService)\n/* harmony export */ });\n/* harmony import */ var _lib_stripe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/stripe */ \"(rsc)/./src/lib/stripe.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _lib_notifications_credit_purchase_notifications__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/notifications/credit-purchase-notifications */ \"(rsc)/./src/lib/notifications/credit-purchase-notifications.ts\");\n/* harmony import */ var _lib_database_credit_transaction_manager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/database/credit-transaction-manager */ \"(rsc)/./src/lib/database/credit-transaction-manager.ts\");\n\n\n// Note: Using string literals instead of enums for SQLite compatibility\n\n\nclass CreditPaymentService {\n    /**\n   * Create or retrieve Stripe customer for user\n   */ static async getOrCreateStripeCustomer(userId, email) {\n        const user = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n            where: {\n                id: userId\n            },\n            select: {\n                stripeCustomerId: true\n            }\n        });\n        if (user?.stripeCustomerId) {\n            return user.stripeCustomerId;\n        }\n        // Create new Stripe customer\n        const customer = await _lib_stripe__WEBPACK_IMPORTED_MODULE_0__.stripe.customers.create({\n            email,\n            metadata: {\n                userId\n            }\n        });\n        // Update user with Stripe customer ID\n        await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: {\n                stripeCustomerId: customer.id\n            }\n        });\n        return customer.id;\n    }\n    /**\n   * Get user's saved payment methods\n   */ static async getUserPaymentMethods(stripeCustomerId) {\n        try {\n            const paymentMethods = await _lib_stripe__WEBPACK_IMPORTED_MODULE_0__.stripe.paymentMethods.list({\n                customer: stripeCustomerId,\n                type: \"card\"\n            });\n            return paymentMethods.data.map((pm)=>({\n                    id: pm.id,\n                    type: pm.type,\n                    card: pm.card ? {\n                        brand: pm.card.brand,\n                        last4: pm.card.last4,\n                        exp_month: pm.card.exp_month,\n                        exp_year: pm.card.exp_year\n                    } : undefined\n                }));\n        } catch (error) {\n            console.error(\"Error fetching payment methods:\", error);\n            return [];\n        }\n    }\n    /**\n   * Create payment intent with enhanced error handling\n   */ static async createPaymentIntent(request, userId, stripeCustomerId) {\n        try {\n            // Get credit package\n            const creditPackage = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.creditPackage.findUnique({\n                where: {\n                    id: request.packageId\n                }\n            });\n            if (!creditPackage || !creditPackage.isActive) {\n                return {\n                    purchase: null,\n                    requiresAction: false,\n                    status: \"failed\",\n                    error: \"Credit package not found or inactive\"\n                };\n            }\n            // Determine payment method\n            let paymentMethodId = request.paymentMethodId;\n            if (request.useStoredPaymentMethod && request.storedPaymentMethodId) {\n                paymentMethodId = request.storedPaymentMethodId;\n            }\n            if (!paymentMethodId) {\n                return {\n                    purchase: null,\n                    requiresAction: false,\n                    status: \"requires_payment_method\",\n                    error: \"Payment method required\"\n                };\n            }\n            // Create payment intent\n            const paymentIntentData = {\n                amount: Math.round(creditPackage.price * 100),\n                currency: creditPackage.currency,\n                customer: stripeCustomerId,\n                payment_method: paymentMethodId,\n                confirmation_method: \"manual\",\n                confirm: true,\n                return_url: `${process.env.NEXTAUTH_URL}/dashboard/credits`,\n                metadata: {\n                    type: \"credit_purchase\",\n                    packageId: creditPackage.id,\n                    userId,\n                    credits: creditPackage.credits.toString(),\n                    bonusCredits: creditPackage.bonusCredits.toString()\n                }\n            };\n            // Save payment method if requested\n            if (request.savePaymentMethod && !request.useStoredPaymentMethod) {\n                paymentIntentData.setup_future_usage = \"off_session\";\n            }\n            const paymentIntent = await _lib_stripe__WEBPACK_IMPORTED_MODULE_0__.stripe.paymentIntents.create(paymentIntentData);\n            // Use transaction manager for safe credit purchase processing\n            const totalCredits = creditPackage.credits + creditPackage.bonusCredits;\n            const { purchase: creditPurchase, newBalance } = await _lib_database_credit_transaction_manager__WEBPACK_IMPORTED_MODULE_3__.CreditTransactionManager.executeCreditPurchase({\n                userId,\n                packageId: creditPackage.id,\n                credits: creditPackage.credits,\n                bonusCredits: creditPackage.bonusCredits,\n                totalCredits,\n                amount: creditPackage.price,\n                currency: creditPackage.currency,\n                stripePaymentId: paymentIntent.id,\n                status: this.mapStripeStatusToPaymentStatus(paymentIntent.status)\n            });\n            // Send notification for successful purchases\n            if (paymentIntent.status === \"succeeded\") {\n                try {\n                    await _lib_notifications_credit_purchase_notifications__WEBPACK_IMPORTED_MODULE_2__.CreditPurchaseNotificationService.sendPurchaseConfirmation({\n                        purchaseId: creditPurchase.id,\n                        userEmail: creditPurchase.user.email,\n                        userName: creditPurchase.user.name || \"Valued Customer\",\n                        packageName: creditPurchase.package.name,\n                        credits: creditPurchase.credits,\n                        bonusCredits: creditPurchase.bonusCredits,\n                        totalCredits: creditPurchase.totalCredits,\n                        amount: creditPurchase.amount,\n                        currency: creditPurchase.currency,\n                        purchaseDate: creditPurchase.createdAt,\n                        stripePaymentId: creditPurchase.stripePaymentId || undefined\n                    });\n                } catch (emailError) {\n                    console.error(\"Failed to send purchase confirmation email:\", emailError);\n                // Don't throw - email failure shouldn't fail the purchase\n                }\n            }\n            return {\n                purchase: creditPurchase,\n                clientSecret: paymentIntent.client_secret,\n                requiresAction: paymentIntent.status === \"requires_action\",\n                status: paymentIntent.status\n            };\n        } catch (error) {\n            console.error(\"Payment intent creation error:\", error);\n            // Handle specific Stripe errors\n            if (error.type === \"StripeCardError\") {\n                return {\n                    purchase: null,\n                    requiresAction: false,\n                    status: \"failed\",\n                    error: error.message || \"Your card was declined\"\n                };\n            }\n            if (error.type === \"StripeInvalidRequestError\") {\n                return {\n                    purchase: null,\n                    requiresAction: false,\n                    status: \"failed\",\n                    error: \"Invalid payment information\"\n                };\n            }\n            return {\n                purchase: null,\n                requiresAction: false,\n                status: \"failed\",\n                error: \"Payment processing failed. Please try again.\"\n            };\n        }\n    }\n    /**\n   * Process successful credit purchase (legacy method - now handled by transaction manager)\n   * @deprecated Use CreditTransactionManager.executeCreditPurchase instead\n   */ static async processSuccessfulCreditPurchase(purchaseId) {\n        console.warn(\"processSuccessfulCreditPurchase is deprecated. Use CreditTransactionManager.executeCreditPurchase instead.\");\n        const purchase = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.prisma.creditPurchase.findUnique({\n            where: {\n                id: purchaseId\n            },\n            include: {\n                user: true,\n                package: true\n            }\n        });\n        if (!purchase) {\n            throw new Error(\"Purchase not found\");\n        }\n        // Use the new transaction manager for safety\n        await _lib_database_credit_transaction_manager__WEBPACK_IMPORTED_MODULE_3__.CreditTransactionManager.executeCreditRefund(purchase.userId, purchase.totalCredits, `Credit purchase: ${purchase.package.name}`, \"purchase\", purchase.id, {\n            packageName: purchase.package.name,\n            baseCredits: purchase.credits,\n            bonusCredits: purchase.bonusCredits,\n            stripePaymentId: purchase.stripePaymentId\n        });\n        // Send purchase confirmation email\n        try {\n            await _lib_notifications_credit_purchase_notifications__WEBPACK_IMPORTED_MODULE_2__.CreditPurchaseNotificationService.sendPurchaseConfirmation({\n                purchaseId: purchase.id,\n                userEmail: purchase.user.email,\n                userName: purchase.user.name || \"Valued Customer\",\n                packageName: purchase.package.name,\n                credits: purchase.credits,\n                bonusCredits: purchase.bonusCredits,\n                totalCredits: purchase.totalCredits,\n                amount: purchase.amount,\n                currency: purchase.currency,\n                purchaseDate: purchase.createdAt,\n                stripePaymentId: purchase.stripePaymentId || undefined\n            });\n        } catch (emailError) {\n            console.error(\"Failed to send purchase confirmation email:\", emailError);\n        // Don't throw - email failure shouldn't fail the purchase\n        }\n    }\n    /**\n   * Map Stripe payment status to our PaymentStatus string\n   */ static mapStripeStatusToPaymentStatus(stripeStatus) {\n        switch(stripeStatus){\n            case \"succeeded\":\n                return \"COMPLETED\";\n            case \"requires_action\":\n            case \"requires_confirmation\":\n            case \"requires_payment_method\":\n                return \"PENDING\";\n            case \"canceled\":\n                return \"CANCELED\";\n            case \"processing\":\n                return \"PENDING\";\n            default:\n                return \"FAILED\";\n        }\n    }\n    /**\n   * Handle payment method attachment for future use\n   */ static async attachPaymentMethodToCustomer(paymentMethodId, customerId) {\n        try {\n            await _lib_stripe__WEBPACK_IMPORTED_MODULE_0__.stripe.paymentMethods.attach(paymentMethodId, {\n                customer: customerId\n            });\n        } catch (error) {\n            console.error(\"Error attaching payment method:\", error);\n        // Don't throw - this is not critical for the purchase\n        }\n    }\n    /**\n   * Detach payment method from customer\n   */ static async detachPaymentMethod(paymentMethodId) {\n        try {\n            await _lib_stripe__WEBPACK_IMPORTED_MODULE_0__.stripe.paymentMethods.detach(paymentMethodId);\n        } catch (error) {\n            console.error(\"Error detaching payment method:\", error);\n            throw new Error(\"Failed to remove payment method\");\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/payment/credit-payment-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe.ts":
/*!***************************!*\
  !*** ./src/lib/stripe.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   REVENUE_SHARE: () => (/* binding */ REVENUE_SHARE),\n/* harmony export */   STRIPE_PRICE_IDS: () => (/* binding */ STRIPE_PRICE_IDS),\n/* harmony export */   SUBSCRIPTION_TIERS: () => (/* binding */ SUBSCRIPTION_TIERS),\n/* harmony export */   calculateRevenueSplit: () => (/* binding */ calculateRevenueSplit),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   getStripe: () => (/* binding */ getStripe),\n/* harmony export */   stripe: () => (/* binding */ stripe),\n/* harmony export */   verifyStripeWebhook: () => (/* binding */ verifyStripeWebhook)\n/* harmony export */ });\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n/* harmony import */ var _stripe_stripe_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @stripe/stripe-js */ \"(rsc)/./node_modules/@stripe/stripe-js/lib/index.mjs\");\n\n\n// Server-side Stripe instance\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_0__[\"default\"](process.env.STRIPE_SECRET_KEY, {\n    apiVersion: \"2024-06-20\",\n    typescript: true\n});\n// Client-side Stripe instance\nlet stripePromise;\nconst getStripe = ()=>{\n    if (!stripePromise) {\n        stripePromise = (0,_stripe_stripe_js__WEBPACK_IMPORTED_MODULE_1__.loadStripe)(\"pk_test_51RZ18cE7j6iXdCGyykJ9YwinC6IlOmaUNxcuMdDDWaNuzugT4uhBIbQ3dg1bxb5J8DYUVnepXotfEswjis0Z0knL00Tmsa0Jlj\");\n    }\n    return stripePromise;\n};\n// Stripe webhook signature verification\nconst verifyStripeWebhook = (payload, signature)=>{\n    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;\n    return stripe.webhooks.constructEvent(payload, signature, webhookSecret);\n};\n// Subscription tier configurations\nconst SUBSCRIPTION_TIERS = {\n    FREE: {\n        name: \"Free\",\n        description: \"Access to free content only\",\n        price: 0,\n        yearlyPrice: 0,\n        features: [\n            \"Read free novels and chapters\",\n            \"Basic reading progress tracking\",\n            \"Add to library\"\n        ]\n    },\n    PREMIUM: {\n        name: \"Premium\",\n        description: \"Access to premium content and features\",\n        price: 9.99,\n        yearlyPrice: 99.99,\n        features: [\n            \"All free features\",\n            \"Access to premium novels and chapters\",\n            \"Ad-free reading experience\",\n            \"Early access to new chapters\",\n            \"Support your favorite authors\"\n        ]\n    },\n    PREMIUM_PLUS: {\n        name: \"Premium Plus\",\n        description: \"All premium features plus exclusive content\",\n        price: 19.99,\n        yearlyPrice: 199.99,\n        features: [\n            \"All premium features\",\n            \"Exclusive premium plus content\",\n            \"Direct messaging with authors\",\n            \"Priority customer support\",\n            \"Beta access to new features\"\n        ]\n    }\n};\n// Revenue sharing configuration\nconst REVENUE_SHARE = {\n    PLATFORM_PERCENTAGE: 30,\n    AUTHOR_PERCENTAGE: 70,\n    MINIMUM_PAYOUT_AMOUNT: 50\n};\n// Stripe price IDs (these would be set in environment variables in production)\nconst STRIPE_PRICE_IDS = {\n    PREMIUM_MONTHLY: process.env.STRIPE_PREMIUM_MONTHLY_PRICE_ID,\n    PREMIUM_YEARLY: process.env.STRIPE_PREMIUM_YEARLY_PRICE_ID,\n    PREMIUM_PLUS_MONTHLY: process.env.STRIPE_PREMIUM_PLUS_MONTHLY_PRICE_ID,\n    PREMIUM_PLUS_YEARLY: process.env.STRIPE_PREMIUM_PLUS_YEARLY_PRICE_ID\n};\n// Helper functions\nconst formatCurrency = (amount, currency = \"USD\")=>{\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency\n    }).format(amount);\n};\nconst calculateRevenueSplit = (amount)=>{\n    const platformFee = amount * REVENUE_SHARE.PLATFORM_PERCENTAGE / 100;\n    const authorEarning = amount - platformFee;\n    return {\n        platformFee,\n        authorEarning\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@aws-sdk","vendor-chunks/@stripe","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/zod","vendor-chunks/@smithy","vendor-chunks/stripe","vendor-chunks/nodemailer","vendor-chunks/@sendgrid","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/qs","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/axios","vendor-chunks/object-inspect","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/mailgun.js","vendor-chunks/supports-color","vendor-chunks/strnum","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/safe-buffer","vendor-chunks/resend","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/deepmerge","vendor-chunks/combined-stream","vendor-chunks/call-bound"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcredits%2Fpurchase%2Froute&page=%2Fapi%2Fcredits%2Fpurchase%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcredits%2Fpurchase%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();