"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/novels/[id]/chapters/route";
exports.ids = ["app/api/novels/[id]/chapters/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "./action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_id_chapters_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/novels/[id]/chapters/route.ts */ \"(rsc)/./src/app/api/novels/[id]/chapters/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/novels/[id]/chapters/route\",\n        pathname: \"/api/novels/[id]/chapters\",\n        filename: \"route\",\n        bundlePath: \"app/api/novels/[id]/chapters/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/app/api/novels/[id]/chapters/route.ts\",\n    nextConfigOutput,\n    userland: _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_id_chapters_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/novels/[id]/chapters/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/novels/[id]/chapters/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/novels/[id]/chapters/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nasync function GET(request, { params }) {\n    const { id: novelId } = params;\n    const { searchParams } = new URL(request.url);\n    const includeUnpublished = searchParams.get(\"includeUnpublished\") === \"true\";\n    try {\n        // Get novel and check permissions\n        const novel = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.novel.findUnique({\n            where: {\n                id: novelId\n            },\n            select: {\n                id: true,\n                title: true,\n                authorId: true,\n                status: true\n            }\n        });\n        if (!novel) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Novel not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Check authentication for unpublished content\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        const isAuthor = session?.user?.id === novel.authorId;\n        // Determine chapter status filter\n        let chapterStatusFilter = [\n            _prisma_client__WEBPACK_IMPORTED_MODULE_4__.ChapterStatus.PUBLISHED\n        ];\n        if (includeUnpublished && isAuthor) {\n            chapterStatusFilter = [\n                _prisma_client__WEBPACK_IMPORTED_MODULE_4__.ChapterStatus.PUBLISHED,\n                _prisma_client__WEBPACK_IMPORTED_MODULE_4__.ChapterStatus.DRAFT\n            ];\n        }\n        // Only allow access to unpublished novels for authors\n        if (novel.status !== _prisma_client__WEBPACK_IMPORTED_MODULE_4__.NovelStatus.PUBLISHED && !isAuthor) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Novel not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Fetch chapters\n        const chapters = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.chapter.findMany({\n            where: {\n                novelId,\n                status: {\n                    in: chapterStatusFilter\n                }\n            },\n            select: {\n                id: true,\n                title: true,\n                order: true,\n                status: true,\n                createdAt: true,\n                updatedAt: true,\n                // Only include content for authors\n                ...isAuthor && {\n                    content: true\n                }\n            },\n            orderBy: {\n                order: \"asc\"\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            novel: {\n                id: novel.id,\n                title: novel.title,\n                status: novel.status\n            },\n            chapters,\n            totalChapters: chapters.length\n        });\n    } catch (error) {\n        console.error(\"Error fetching chapters:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch chapters\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request, { params }) {\n    const { id: novelId } = params;\n    try {\n        // Check authentication\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user || session.user.role !== \"AUTHOR\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Verify novel ownership\n        const novel = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.novel.findFirst({\n            where: {\n                id: novelId,\n                authorId: session.user.id\n            }\n        });\n        if (!novel) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Novel not found or you don't have permission to edit it\"\n            }, {\n                status: 404\n            });\n        }\n        let body;\n        try {\n            const text = await request.text();\n            console.log(\"Request body text:\", text);\n            body = text ? JSON.parse(text) : {};\n        } catch (error) {\n            console.error(\"JSON parsing error:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid JSON in request body\"\n            }, {\n                status: 400\n            });\n        }\n        if (!body || typeof body !== \"object\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Request body must be a valid JSON object\"\n            }, {\n                status: 400\n            });\n        }\n        const { title, content } = body;\n        // Validate required fields\n        if (!title || typeof title !== \"string\" || title.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Title is required and must be a non-empty string\"\n            }, {\n                status: 400\n            });\n        }\n        if (!content || typeof content !== \"string\" || content.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Content is required and must be a non-empty string\"\n            }, {\n                status: 400\n            });\n        }\n        // Get the next order number\n        const lastChapter = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.chapter.findFirst({\n            where: {\n                novelId\n            },\n            orderBy: {\n                order: \"desc\"\n            },\n            select: {\n                order: true\n            }\n        });\n        const nextOrder = (lastChapter?.order || 0) + 1;\n        // Create chapter\n        const chapter = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.chapter.create({\n            data: {\n                title: title.trim(),\n                content: content.trim(),\n                order: nextOrder,\n                novelId,\n                status: _prisma_client__WEBPACK_IMPORTED_MODULE_4__.ChapterStatus.DRAFT\n            },\n            include: {\n                novel: {\n                    select: {\n                        id: true,\n                        title: true,\n                        authorId: true\n                    }\n                }\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: chapter,\n            message: \"Chapter created successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error creating chapter:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to create chapter\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/novels/[id]/chapters/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async session ({ session, user }) {\n            if (session.user) {\n                session.user.id = user.id;\n                session.user.role = user.role || \"READER\";\n            }\n            return session;\n        },\n        async signIn ({ user, account, profile }) {\n            // Allow sign in - user creation will be handled in API endpoints if needed\n            return true;\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    session: {\n        strategy: \"database\"\n    },\n    events: {\n        async signIn ({ user, account, profile }) {\n            console.log(\"User signed in:\", {\n                user: user.email,\n                provider: account?.provider\n            });\n        },\n        async signOut ({ session }) {\n            console.log(\"User signed out:\", session?.user?.email);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9zcmMvbGliL2RiLnRzPzllNGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();