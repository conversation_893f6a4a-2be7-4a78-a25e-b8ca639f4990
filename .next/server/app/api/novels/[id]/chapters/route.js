"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/novels/[id]/chapters/route";
exports.ids = ["app/api/novels/[id]/chapters/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "./action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_id_chapters_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/novels/[id]/chapters/route.ts */ \"(rsc)/./src/app/api/novels/[id]/chapters/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/novels/[id]/chapters/route\",\n        pathname: \"/api/novels/[id]/chapters\",\n        filename: \"route\",\n        bundlePath: \"app/api/novels/[id]/chapters/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/app/api/novels/[id]/chapters/route.ts\",\n    nextConfigOutput,\n    userland: _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_id_chapters_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/novels/[id]/chapters/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/novels/[id]/chapters/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/novels/[id]/chapters/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n\n\n\n\nasync function GET(request, { params }) {\n    const { id: novelId } = params;\n    const { searchParams } = new URL(request.url);\n    const includeUnpublished = searchParams.get(\"includeUnpublished\") === \"true\";\n    try {\n        // Get novel and check permissions\n        const novel = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.novel.findUnique({\n            where: {\n                id: novelId\n            },\n            select: {\n                id: true,\n                title: true,\n                authorId: true,\n                status: true\n            }\n        });\n        if (!novel) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Novel not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Check authentication for unpublished content\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        const isAuthor = session?.user?.id === novel.authorId;\n        // Determine chapter status filter\n        let chapterStatusFilter = [\n            \"PUBLISHED\"\n        ];\n        if (includeUnpublished && isAuthor) {\n            chapterStatusFilter = [\n                \"PUBLISHED\",\n                \"DRAFT\"\n            ];\n        }\n        // Only allow access to unpublished novels for authors\n        if (novel.status !== \"PUBLISHED\" && !isAuthor) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Novel not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Fetch chapters\n        const chapters = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.chapter.findMany({\n            where: {\n                novelId,\n                status: {\n                    in: chapterStatusFilter\n                }\n            },\n            select: {\n                id: true,\n                title: true,\n                order: true,\n                status: true,\n                createdAt: true,\n                updatedAt: true,\n                // Only include content for authors\n                ...isAuthor && {\n                    content: true\n                }\n            },\n            orderBy: {\n                order: \"asc\"\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            novel: {\n                id: novel.id,\n                title: novel.title,\n                status: novel.status\n            },\n            chapters,\n            totalChapters: chapters.length\n        });\n    } catch (error) {\n        console.error(\"Error fetching chapters:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch chapters\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request, { params }) {\n    const { id: novelId } = params;\n    try {\n        // Check authentication\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user || session.user.role !== \"AUTHOR\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Verify novel ownership\n        const novel = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.novel.findFirst({\n            where: {\n                id: novelId,\n                authorId: session.user.id\n            }\n        });\n        if (!novel) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Novel not found or you don't have permission to edit it\"\n            }, {\n                status: 404\n            });\n        }\n        let body;\n        try {\n            const text = await request.text();\n            console.log(\"Request body text:\", text);\n            body = text ? JSON.parse(text) : {};\n        } catch (error) {\n            console.error(\"JSON parsing error:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid JSON in request body\"\n            }, {\n                status: 400\n            });\n        }\n        if (!body || typeof body !== \"object\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Request body must be a valid JSON object\"\n            }, {\n                status: 400\n            });\n        }\n        const { title, content } = body;\n        // Validate required fields\n        if (!title || typeof title !== \"string\" || title.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Title is required and must be a non-empty string\"\n            }, {\n                status: 400\n            });\n        }\n        if (!content || typeof content !== \"string\" || content.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Content is required and must be a non-empty string\"\n            }, {\n                status: 400\n            });\n        }\n        // Get the next order number\n        const lastChapter = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.chapter.findFirst({\n            where: {\n                novelId\n            },\n            orderBy: {\n                order: \"desc\"\n            },\n            select: {\n                order: true\n            }\n        });\n        const nextOrder = (lastChapter?.order || 0) + 1;\n        // Create chapter\n        const chapter = await _lib_db__WEBPACK_IMPORTED_MODULE_3__.prisma.chapter.create({\n            data: {\n                title: title.trim(),\n                content: content.trim(),\n                order: nextOrder,\n                novelId,\n                status: \"DRAFT\"\n            },\n            include: {\n                novel: {\n                    select: {\n                        id: true,\n                        title: true,\n                        authorId: true\n                    }\n                }\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: chapter,\n            message: \"Chapter created successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error creating chapter:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to create chapter\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/novels/[id]/chapters/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async session ({ session, user }) {\n            if (session.user) {\n                session.user.id = user.id;\n                session.user.role = user.role || \"READER\";\n            }\n            return session;\n        },\n        async signIn ({ user, account, profile }) {\n            // Allow sign in - user creation will be handled in API endpoints if needed\n            return true;\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    session: {\n        strategy: \"database\"\n    },\n    events: {\n        async signIn ({ user, account, profile }) {\n            console.log(\"User signed in:\", {\n                user: user.email,\n                provider: account?.provider\n            });\n        },\n        async signOut ({ session }) {\n            console.log(\"User signed out:\", session?.user?.email);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2F1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUN5RDtBQUNGO0FBQ3RCO0FBRTFCLE1BQU1HLGNBQStCO0lBQzFDQyxTQUFTSix3RUFBYUEsQ0FBQ0UsMkNBQU1BO0lBQzdCRyxXQUFXO1FBQ1RKLHNFQUFjQSxDQUFDO1lBQ2JLLFVBQVVDLFFBQVFDLEdBQUcsQ0FBQ0MsZ0JBQWdCO1lBQ3RDQyxjQUFjSCxRQUFRQyxHQUFHLENBQUNHLG9CQUFvQjtRQUNoRDtLQUNEO0lBQ0RDLFdBQVc7UUFDVCxNQUFNQyxTQUFRLEVBQUVBLE9BQU8sRUFBRUMsSUFBSSxFQUFFO1lBQzdCLElBQUlELFFBQVFDLElBQUksRUFBRTtnQkFDaEJELFFBQVFDLElBQUksQ0FBQ0MsRUFBRSxHQUFHRCxLQUFLQyxFQUFFO2dCQUN6QkYsUUFBUUMsSUFBSSxDQUFDRSxJQUFJLEdBQUcsS0FBY0EsSUFBSSxJQUFJO1lBQzVDO1lBQ0EsT0FBT0g7UUFDVDtRQUNBLE1BQU1JLFFBQU8sRUFBRUgsSUFBSSxFQUFFSSxPQUFPLEVBQUVDLE9BQU8sRUFBRTtZQUNyQywyRUFBMkU7WUFDM0UsT0FBTztRQUNUO1FBQ0EsTUFBTUMsS0FBSSxFQUFFQyxLQUFLLEVBQUVQLElBQUksRUFBRTtZQUN2QixJQUFJQSxNQUFNO2dCQUNSTyxNQUFNTCxJQUFJLEdBQUcsS0FBY0EsSUFBSTtZQUNqQztZQUNBLE9BQU9LO1FBQ1Q7SUFDRjtJQUNBQyxPQUFPO1FBQ0xMLFFBQVE7UUFDUk0sT0FBTztJQUNUO0lBQ0FWLFNBQVM7UUFDUFcsVUFBVTtJQUNaO0lBQ0FDLFFBQVE7UUFDTixNQUFNUixRQUFPLEVBQUVILElBQUksRUFBRUksT0FBTyxFQUFFQyxPQUFPLEVBQUU7WUFDckNPLFFBQVFDLEdBQUcsQ0FBQyxtQkFBbUI7Z0JBQUViLE1BQU1BLEtBQUtjLEtBQUs7Z0JBQUVDLFVBQVVYLFNBQVNXO1lBQVM7UUFDakY7UUFDQSxNQUFNQyxTQUFRLEVBQUVqQixPQUFPLEVBQUU7WUFDdkJhLFFBQVFDLEdBQUcsQ0FBQyxvQkFBb0JkLFNBQVNDLE1BQU1jO1FBQ2pEO0lBQ0Y7QUFDRixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmxhY2stYmxvZy8uL3NyYy9saWIvYXV0aC50cz82NjkyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRBdXRoT3B0aW9ucyB9IGZyb20gXCJuZXh0LWF1dGhcIlxuaW1wb3J0IHsgUHJpc21hQWRhcHRlciB9IGZyb20gXCJAbmV4dC1hdXRoL3ByaXNtYS1hZGFwdGVyXCJcbmltcG9ydCBHb29nbGVQcm92aWRlciBmcm9tIFwibmV4dC1hdXRoL3Byb3ZpZGVycy9nb29nbGVcIlxuaW1wb3J0IHsgcHJpc21hIH0gZnJvbSBcIkAvbGliL2RiXCJcblxuZXhwb3J0IGNvbnN0IGF1dGhPcHRpb25zOiBOZXh0QXV0aE9wdGlvbnMgPSB7XG4gIGFkYXB0ZXI6IFByaXNtYUFkYXB0ZXIocHJpc21hKSxcbiAgcHJvdmlkZXJzOiBbXG4gICAgR29vZ2xlUHJvdmlkZXIoe1xuICAgICAgY2xpZW50SWQ6IHByb2Nlc3MuZW52LkdPT0dMRV9DTElFTlRfSUQhLFxuICAgICAgY2xpZW50U2VjcmV0OiBwcm9jZXNzLmVudi5HT09HTEVfQ0xJRU5UX1NFQ1JFVCEsXG4gICAgfSksXG4gIF0sXG4gIGNhbGxiYWNrczoge1xuICAgIGFzeW5jIHNlc3Npb24oeyBzZXNzaW9uLCB1c2VyIH0pIHtcbiAgICAgIGlmIChzZXNzaW9uLnVzZXIpIHtcbiAgICAgICAgc2Vzc2lvbi51c2VyLmlkID0gdXNlci5pZFxuICAgICAgICBzZXNzaW9uLnVzZXIucm9sZSA9ICh1c2VyIGFzIGFueSkucm9sZSB8fCBcIlJFQURFUlwiXG4gICAgICB9XG4gICAgICByZXR1cm4gc2Vzc2lvblxuICAgIH0sXG4gICAgYXN5bmMgc2lnbkluKHsgdXNlciwgYWNjb3VudCwgcHJvZmlsZSB9KSB7XG4gICAgICAvLyBBbGxvdyBzaWduIGluIC0gdXNlciBjcmVhdGlvbiB3aWxsIGJlIGhhbmRsZWQgaW4gQVBJIGVuZHBvaW50cyBpZiBuZWVkZWRcbiAgICAgIHJldHVybiB0cnVlXG4gICAgfSxcbiAgICBhc3luYyBqd3QoeyB0b2tlbiwgdXNlciB9KSB7XG4gICAgICBpZiAodXNlcikge1xuICAgICAgICB0b2tlbi5yb2xlID0gKHVzZXIgYXMgYW55KS5yb2xlXG4gICAgICB9XG4gICAgICByZXR1cm4gdG9rZW5cbiAgICB9LFxuICB9LFxuICBwYWdlczoge1xuICAgIHNpZ25JbjogXCIvYXV0aC9zaWduaW5cIixcbiAgICBlcnJvcjogXCIvYXV0aC9lcnJvclwiLFxuICB9LFxuICBzZXNzaW9uOiB7XG4gICAgc3RyYXRlZ3k6IFwiZGF0YWJhc2VcIixcbiAgfSxcbiAgZXZlbnRzOiB7XG4gICAgYXN5bmMgc2lnbkluKHsgdXNlciwgYWNjb3VudCwgcHJvZmlsZSB9KSB7XG4gICAgICBjb25zb2xlLmxvZyhcIlVzZXIgc2lnbmVkIGluOlwiLCB7IHVzZXI6IHVzZXIuZW1haWwsIHByb3ZpZGVyOiBhY2NvdW50Py5wcm92aWRlciB9KVxuICAgIH0sXG4gICAgYXN5bmMgc2lnbk91dCh7IHNlc3Npb24gfSkge1xuICAgICAgY29uc29sZS5sb2coXCJVc2VyIHNpZ25lZCBvdXQ6XCIsIHNlc3Npb24/LnVzZXI/LmVtYWlsKVxuICAgIH0sXG4gIH0sXG59Il0sIm5hbWVzIjpbIlByaXNtYUFkYXB0ZXIiLCJHb29nbGVQcm92aWRlciIsInByaXNtYSIsImF1dGhPcHRpb25zIiwiYWRhcHRlciIsInByb3ZpZGVycyIsImNsaWVudElkIiwicHJvY2VzcyIsImVudiIsIkdPT0dMRV9DTElFTlRfSUQiLCJjbGllbnRTZWNyZXQiLCJHT09HTEVfQ0xJRU5UX1NFQ1JFVCIsImNhbGxiYWNrcyIsInNlc3Npb24iLCJ1c2VyIiwiaWQiLCJyb2xlIiwic2lnbkluIiwiYWNjb3VudCIsInByb2ZpbGUiLCJqd3QiLCJ0b2tlbiIsInBhZ2VzIiwiZXJyb3IiLCJzdHJhdGVneSIsImV2ZW50cyIsImNvbnNvbGUiLCJsb2ciLCJlbWFpbCIsInByb3ZpZGVyIiwic2lnbk91dCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9zcmMvbGliL2RiLnRzPzllNGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/oauth","vendor-chunks/preact","vendor-chunks/@next-auth","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/cookie","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();