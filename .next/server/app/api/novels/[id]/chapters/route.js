/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/novels/[id]/chapters/route";
exports.ids = ["app/api/novels/[id]/chapters/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "./action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_id_chapters_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/novels/[id]/chapters/route.ts */ \"(rsc)/./src/app/api/novels/[id]/chapters/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/novels/[id]/chapters/route\",\n        pathname: \"/api/novels/[id]/chapters\",\n        filename: \"route\",\n        bundlePath: \"app/api/novels/[id]/chapters/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/app/api/novels/[id]/chapters/route.ts\",\n    nextConfigOutput,\n    userland: _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_id_chapters_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/novels/[id]/chapters/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/novels/[id]/chapters/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/novels/[id]/chapters/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n/* harmony import */ var _paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @paralleldrive/cuid2 */ \"(rsc)/./node_modules/@paralleldrive/cuid2/index.js\");\n\n\n\n\n\nasync function GET(request, { params }) {\n    const { id: novelId } = params;\n    const { searchParams } = new URL(request.url);\n    const includeUnpublished = searchParams.get(\"includeUnpublished\") === \"true\";\n    try {\n        // Get novel and check permissions using Supabase\n        const { data: novel, error: novelError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").select(\"id, title, author_id, status\").eq(\"id\", novelId).single();\n        if (novelError || !novel) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Novel not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Check authentication for unpublished content\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        const isAuthor = session?.user?.id === novel.author_id;\n        // Determine chapter status filter\n        let chapterStatusFilter = [\n            \"PUBLISHED\"\n        ];\n        // Authors can always see their own draft chapters\n        if (isAuthor) {\n            chapterStatusFilter = [\n                \"PUBLISHED\",\n                \"DRAFT\"\n            ];\n        }\n        // Only allow access to unpublished novels for authors\n        if (novel.status !== \"PUBLISHED\" && !isAuthor) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Novel not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Fetch chapters using Supabase\n        let chaptersQuery = _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"chapters\").select(`\n        id,\n        title,\n        order,\n        status,\n        created_at,\n        updated_at\n        ${isAuthor ? \", content\" : \"\"}\n      `).eq(\"novel_id\", novelId).in(\"status\", chapterStatusFilter).order(\"order\", {\n            ascending: true\n        });\n        const { data: chapters, error: chaptersError } = await chaptersQuery;\n        if (chaptersError) {\n            console.error(\"Error fetching chapters:\", chaptersError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch chapters\"\n            }, {\n                status: 500\n            });\n        }\n        // Transform chapters to match expected format\n        const transformedChapters = chapters?.map((chapter)=>({\n                id: chapter.id,\n                title: chapter.title,\n                order: chapter.order,\n                status: chapter.status,\n                createdAt: chapter.created_at,\n                updatedAt: chapter.updated_at,\n                ...isAuthor && chapter.content && {\n                    content: chapter.content\n                }\n            })) || [];\n        const response = {\n            novel: {\n                id: novel.id,\n                title: novel.title,\n                status: novel.status\n            },\n            chapters: transformedChapters,\n            totalChapters: transformedChapters.length\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error(\"Error fetching chapters:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch chapters\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request, { params }) {\n    const { id: novelId } = params;\n    try {\n        // Check authentication\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user || session.user.role !== \"AUTHOR\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Verify novel ownership using Supabase\n        const { data: novel, error: novelError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").select(\"id, title, author_id\").eq(\"id\", novelId).eq(\"author_id\", session.user.id).single();\n        if (novelError || !novel) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Novel not found or you don't have permission to edit it\"\n            }, {\n                status: 404\n            });\n        }\n        let body;\n        try {\n            const text = await request.text();\n            console.log(\"Request body text:\", text);\n            body = text ? JSON.parse(text) : {};\n        } catch (error) {\n            console.error(\"JSON parsing error:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid JSON in request body\"\n            }, {\n                status: 400\n            });\n        }\n        if (!body || typeof body !== \"object\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Request body must be a valid JSON object\"\n            }, {\n                status: 400\n            });\n        }\n        const { title, content } = body;\n        // Validate required fields\n        if (!title || typeof title !== \"string\" || title.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Title is required and must be a non-empty string\"\n            }, {\n                status: 400\n            });\n        }\n        if (!content || typeof content !== \"string\" || content.trim().length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Content is required and must be a non-empty string\"\n            }, {\n                status: 400\n            });\n        }\n        // Get the next order number using Supabase\n        const { data: lastChapter } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"chapters\").select(\"order\").eq(\"novel_id\", novelId).order(\"order\", {\n            ascending: false\n        }).limit(1).single();\n        const nextOrder = (lastChapter?.order || 0) + 1;\n        // Create chapter using Supabase\n        const chapterId = (0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_4__.createId)();\n        const now = new Date().toISOString();\n        const { data: chapter, error: createError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"chapters\").insert({\n            id: chapterId,\n            title: title.trim(),\n            content: content.trim(),\n            order: nextOrder,\n            novel_id: novelId,\n            status: \"DRAFT\",\n            created_at: now,\n            updated_at: now\n        }).select(`\n        *,\n        novels!chapters_novel_id_fkey(id, title, author_id)\n      `).single();\n        if (createError) {\n            console.error(\"Error creating chapter:\", createError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to create chapter\"\n            }, {\n                status: 500\n            });\n        }\n        // Transform chapter to match expected format\n        const transformedChapter = {\n            id: chapter.id,\n            title: chapter.title,\n            content: chapter.content,\n            order: chapter.order,\n            status: chapter.status,\n            novelId: chapter.novel_id,\n            createdAt: chapter.created_at,\n            updatedAt: chapter.updated_at,\n            novel: {\n                id: chapter.novels.id,\n                title: chapter.novels.title,\n                authorId: chapter.novels.author_id\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: transformedChapter,\n            message: \"Chapter created successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error creating chapter:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to create chapter\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/novels/[id]/chapters/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async session ({ session, user }) {\n            if (session.user) {\n                session.user.id = user.id;\n                session.user.role = user.role || \"READER\";\n            }\n            return session;\n        },\n        async signIn ({ user, account, profile }) {\n            // Allow sign in - user creation will be handled in API endpoints if needed\n            return true;\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    session: {\n        strategy: \"database\"\n    },\n    events: {\n        async signIn ({ user, account, profile }) {\n            console.log(\"User signed in:\", {\n                user: user.email,\n                provider: account?.provider\n            });\n        },\n        async signOut ({ session }) {\n            console.log(\"User signed out:\", session?.user?.email);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9zcmMvbGliL2RiLnRzPzllNGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteCoverImage: () => (/* binding */ deleteCoverImage),\n/* harmony export */   getCoverImageUrl: () => (/* binding */ getCoverImageUrl),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   uploadCoverImage: () => (/* binding */ uploadCoverImage)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://itjoywrqviaonrgtcicv.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml0am95d3Jxdmlhb25yZ3RjaWN2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjAzODQsImV4cCI6MjA2NzYzNjM4NH0.wvD0u8gQOV6yc6gbAqUiYczSHaDvkj9PSC6liT4dfeM\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables\");\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// File upload utilities\nasync function uploadCoverImage(file, novelId) {\n    const fileExt = file.name.split(\".\").pop()?.toLowerCase();\n    const fileName = `${novelId}.${fileExt}`;\n    // Validate file type\n    const allowedTypes = [\n        \"jpg\",\n        \"jpeg\",\n        \"png\",\n        \"webp\"\n    ];\n    if (!fileExt || !allowedTypes.includes(fileExt)) {\n        throw new Error(\"Invalid file type. Only JPG, PNG, and WebP files are allowed.\");\n    }\n    // Validate file size (max 5MB)\n    const maxSize = 5 * 1024 * 1024 // 5MB in bytes\n    ;\n    if (file.size > maxSize) {\n        throw new Error(\"File size too large. Maximum size is 5MB.\");\n    }\n    const { data, error } = await supabase.storage.from(\"covers\").upload(fileName, file, {\n        upsert: true,\n        contentType: file.type\n    });\n    if (error) {\n        throw new Error(`Upload failed: ${error.message}`);\n    }\n    // Get public URL\n    const { data: { publicUrl } } = supabase.storage.from(\"covers\").getPublicUrl(fileName);\n    return publicUrl;\n}\nasync function deleteCoverImage(fileName) {\n    const { error } = await supabase.storage.from(\"covers\").remove([\n        fileName\n    ]);\n    if (error) {\n        throw new Error(`Delete failed: ${error.message}`);\n    }\n}\nfunction getCoverImageUrl(fileName) {\n    const { data: { publicUrl } } = supabase.storage.from(\"covers\").getPublicUrl(fileName);\n    return publicUrl;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/jose","vendor-chunks/next-auth","vendor-chunks/@supabase","vendor-chunks/next","vendor-chunks/openid-client","vendor-chunks/@babel","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/oauth","vendor-chunks/@noble","vendor-chunks/@panva","vendor-chunks/isows","vendor-chunks/yallist","vendor-chunks/tr46","vendor-chunks/preact-render-to-string","vendor-chunks/oidc-token-hash","vendor-chunks/@paralleldrive","vendor-chunks/webidl-conversions","vendor-chunks/preact","vendor-chunks/cookie","vendor-chunks/@next-auth"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Fchapters%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();