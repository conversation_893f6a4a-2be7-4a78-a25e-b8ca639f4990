/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/novels/[id]/route";
exports.ids = ["app/api/novels/[id]/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "./action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/novels/[id]/route.ts */ \"(rsc)/./src/app/api/novels/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/novels/[id]/route\",\n        pathname: \"/api/novels/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/novels/[id]/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/app/api/novels/[id]/route.ts\",\n    nextConfigOutput,\n    userland: _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_novels_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/novels/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/novels/[id]/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/novels/[id]/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\n\n\nasync function GET(request, { params }) {\n    try {\n        const { id } = params;\n        // Get session first to determine if user is author\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        // Get novel with author information using Supabase\n        const { data: novel, error: novelError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").select(`\n        *,\n        users!novels_author_id_fkey(id, name, image, bio)\n      `).eq(\"id\", id).single();\n        if (novelError || !novel) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Novel not found\"\n            }, {\n                status: 404\n            });\n        }\n        const isAuthor = session?.user?.id === novel.author_id;\n        // Check if user can access this novel (if it's not published and user is not author)\n        if (novel.status !== \"PUBLISHED\" && !isAuthor) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Novel not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Get chapters with appropriate filtering using Supabase\n        const chaptersQuery = _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"chapters\").select(\"id, title, order, status, created_at, is_premium, required_tier\").eq(\"novel_id\", id).order(\"order\", {\n            ascending: true\n        });\n        // If not author, only show published chapters\n        if (!isAuthor) {\n            chaptersQuery.eq(\"status\", \"PUBLISHED\");\n        }\n        const { data: chapters, error: chaptersError } = await chaptersQuery;\n        if (chaptersError) {\n            console.error(\"Error fetching chapters:\", chaptersError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch chapters\"\n            }, {\n                status: 500\n            });\n        }\n        // Transform the response to match the expected format\n        const novelWithChapters = {\n            id: novel.id,\n            title: novel.title,\n            description: novel.description,\n            synopsis: novel.synopsis,\n            coverImage: novel.cover_image,\n            status: novel.status,\n            genre: novel.genre,\n            tags: novel.tags,\n            authorId: novel.author_id,\n            createdAt: novel.created_at,\n            updatedAt: novel.updated_at,\n            publishedAt: novel.published_at,\n            isPremium: novel.is_premium,\n            requiredTier: novel.required_tier,\n            price: novel.price,\n            creditPrice: novel.credit_price,\n            revenueSharePercent: novel.revenue_share_percent,\n            author: novel.users,\n            chapters: chapters?.map((chapter)=>({\n                    id: chapter.id,\n                    title: chapter.title,\n                    order: chapter.order,\n                    status: chapter.status,\n                    createdAt: chapter.created_at,\n                    isPremium: chapter.is_premium,\n                    requiredTier: chapter.required_tier\n                })) || [],\n            _count: {\n                chapters: chapters?.length || 0\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(novelWithChapters);\n    } catch (error) {\n        console.error(\"Error fetching novel:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch novel\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(request, { params }) {\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n    if (!session?.user || session.user.role !== \"AUTHOR\") {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Unauthorized\"\n        }, {\n            status: 401\n        });\n    }\n    const { id } = params;\n    try {\n        // Verify ownership using Supabase\n        const { data: existingNovel, error: fetchError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").select(\"author_id\").eq(\"id\", id).single();\n        if (fetchError || !existingNovel) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Novel not found\"\n            }, {\n                status: 404\n            });\n        }\n        if (existingNovel.author_id !== session.user.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Forbidden\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const { title, description, synopsis, genre, tags, status, coverImage } = body;\n        const updateData = {\n            updated_at: new Date().toISOString()\n        };\n        // Only update fields that are provided\n        if (title) updateData.title = title.trim();\n        if (description !== undefined) updateData.description = description?.trim() || null;\n        if (synopsis !== undefined) updateData.synopsis = synopsis?.trim() || null;\n        if (genre !== undefined) updateData.genre = genre?.trim() || null;\n        if (tags) updateData.tags = tags;\n        if (status) updateData.status = status;\n        if (coverImage !== undefined) updateData.cover_image = coverImage;\n        // Set published_at if status is being changed to PUBLISHED\n        if (status === \"PUBLISHED\") {\n            updateData.published_at = new Date().toISOString();\n        }\n        const { data: updatedNovel, error: updateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").update(updateData).eq(\"id\", id).select(`\n        *,\n        users!novels_author_id_fkey(id, name, image, bio)\n      `).single();\n        if (updateError) {\n            console.error(\"Error updating novel:\", updateError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to update novel\"\n            }, {\n                status: 500\n            });\n        }\n        // Transform response to match expected format\n        const transformedNovel = {\n            id: updatedNovel.id,\n            title: updatedNovel.title,\n            description: updatedNovel.description,\n            synopsis: updatedNovel.synopsis,\n            coverImage: updatedNovel.cover_image,\n            status: updatedNovel.status,\n            genre: updatedNovel.genre,\n            tags: updatedNovel.tags,\n            authorId: updatedNovel.author_id,\n            createdAt: updatedNovel.created_at,\n            updatedAt: updatedNovel.updated_at,\n            publishedAt: updatedNovel.published_at,\n            isPremium: updatedNovel.is_premium,\n            requiredTier: updatedNovel.required_tier,\n            price: updatedNovel.price,\n            creditPrice: updatedNovel.credit_price,\n            revenueSharePercent: updatedNovel.revenue_share_percent,\n            author: updatedNovel.users\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(transformedNovel);\n    } catch (error) {\n        console.error(\"Error updating novel:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to update novel\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request, { params }) {\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_lib_auth__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n    if (!session?.user || session.user.role !== \"AUTHOR\") {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Unauthorized\"\n        }, {\n            status: 401\n        });\n    }\n    const { id } = params;\n    try {\n        // Verify ownership using Supabase\n        const { data: existingNovel, error: fetchError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").select(\"author_id\").eq(\"id\", id).single();\n        if (fetchError || !existingNovel) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Novel not found\"\n            }, {\n                status: 404\n            });\n        }\n        if (existingNovel.author_id !== session.user.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Forbidden\"\n            }, {\n                status: 403\n            });\n        }\n        const { error: deleteError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from(\"novels\").delete().eq(\"id\", id);\n        if (deleteError) {\n            console.error(\"Error deleting novel:\", deleteError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to delete novel\"\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Novel deleted successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error deleting novel:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to delete novel\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/novels/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var _next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @next-auth/prisma-adapter */ \"(rsc)/./node_modules/@next-auth/prisma-adapter/dist/index.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n\n\n\nconst authOptions = {\n    adapter: (0,_next_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_0__.PrismaAdapter)(_lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async session ({ session, user }) {\n            if (session.user) {\n                session.user.id = user.id;\n                session.user.role = user.role || \"READER\";\n            }\n            return session;\n        },\n        async signIn ({ user, account, profile }) {\n            // Allow sign in - user creation will be handled in API endpoints if needed\n            return true;\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                token.role = user.role;\n            }\n            return token;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\",\n        error: \"/auth/error\"\n    },\n    session: {\n        strategy: \"database\"\n    },\n    events: {\n        async signIn ({ user, account, profile }) {\n            console.log(\"User signed in:\", {\n                user: user.email,\n                provider: account?.provider\n            });\n        },\n        async signOut ({ session }) {\n            console.log(\"User signed out:\", session?.user?.email);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9zcmMvbGliL2RiLnRzPzllNGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deleteCoverImage: () => (/* binding */ deleteCoverImage),\n/* harmony export */   getCoverImageUrl: () => (/* binding */ getCoverImageUrl),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   uploadCoverImage: () => (/* binding */ uploadCoverImage)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://itjoywrqviaonrgtcicv.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml0am95d3Jxdmlhb25yZ3RjaWN2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwNjAzODQsImV4cCI6MjA2NzYzNjM4NH0.wvD0u8gQOV6yc6gbAqUiYczSHaDvkj9PSC6liT4dfeM\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables\");\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// File upload utilities\nasync function uploadCoverImage(file, novelId) {\n    const fileExt = file.name.split(\".\").pop()?.toLowerCase();\n    const fileName = `${novelId}.${fileExt}`;\n    // Validate file type\n    const allowedTypes = [\n        \"jpg\",\n        \"jpeg\",\n        \"png\",\n        \"webp\"\n    ];\n    if (!fileExt || !allowedTypes.includes(fileExt)) {\n        throw new Error(\"Invalid file type. Only JPG, PNG, and WebP files are allowed.\");\n    }\n    // Validate file size (max 5MB)\n    const maxSize = 5 * 1024 * 1024 // 5MB in bytes\n    ;\n    if (file.size > maxSize) {\n        throw new Error(\"File size too large. Maximum size is 5MB.\");\n    }\n    const { data, error } = await supabase.storage.from(\"covers\").upload(fileName, file, {\n        upsert: true,\n        contentType: file.type\n    });\n    if (error) {\n        throw new Error(`Upload failed: ${error.message}`);\n    }\n    // Get public URL\n    const { data: { publicUrl } } = supabase.storage.from(\"covers\").getPublicUrl(fileName);\n    return publicUrl;\n}\nasync function deleteCoverImage(fileName) {\n    const { error } = await supabase.storage.from(\"covers\").remove([\n        fileName\n    ]);\n    if (error) {\n        throw new Error(`Delete failed: ${error.message}`);\n    }\n}\nfunction getCoverImageUrl(fileName) {\n    const { data: { publicUrl } } = supabase.storage.from(\"covers\").getPublicUrl(fileName);\n    return publicUrl;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQW9EO0FBRXBELE1BQU1DLGNBQWNDLDBDQUFvQztBQUN4RCxNQUFNRyxrQkFBa0JILGtOQUF5QztBQUVqRSxJQUFJLENBQUNELGVBQWUsQ0FBQ0ksaUJBQWlCO0lBQ3BDLE1BQU0sSUFBSUUsTUFBTTtBQUNsQjtBQUVPLE1BQU1DLFdBQVdSLG1FQUFZQSxDQUFDQyxhQUFhSSxpQkFBZ0I7QUFFbEUsd0JBQXdCO0FBQ2pCLGVBQWVJLGlCQUFpQkMsSUFBVSxFQUFFQyxPQUFlO0lBQ2hFLE1BQU1DLFVBQVVGLEtBQUtHLElBQUksQ0FBQ0MsS0FBSyxDQUFDLEtBQUtDLEdBQUcsSUFBSUM7SUFDNUMsTUFBTUMsV0FBVyxDQUFDLEVBQUVOLFFBQVEsQ0FBQyxFQUFFQyxRQUFRLENBQUM7SUFFeEMscUJBQXFCO0lBQ3JCLE1BQU1NLGVBQWU7UUFBQztRQUFPO1FBQVE7UUFBTztLQUFPO0lBQ25ELElBQUksQ0FBQ04sV0FBVyxDQUFDTSxhQUFhQyxRQUFRLENBQUNQLFVBQVU7UUFDL0MsTUFBTSxJQUFJTCxNQUFNO0lBQ2xCO0lBRUEsK0JBQStCO0lBQy9CLE1BQU1hLFVBQVUsSUFBSSxPQUFPLEtBQUssZUFBZTs7SUFDL0MsSUFBSVYsS0FBS1csSUFBSSxHQUFHRCxTQUFTO1FBQ3ZCLE1BQU0sSUFBSWIsTUFBTTtJQUNsQjtJQUVBLE1BQU0sRUFBRWUsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNZixTQUFTZ0IsT0FBTyxDQUMzQ0MsSUFBSSxDQUFDLFVBQ0xDLE1BQU0sQ0FBQ1QsVUFBVVAsTUFBTTtRQUN0QmlCLFFBQVE7UUFDUkMsYUFBYWxCLEtBQUttQixJQUFJO0lBQ3hCO0lBRUYsSUFBSU4sT0FBTztRQUNULE1BQU0sSUFBSWhCLE1BQU0sQ0FBQyxlQUFlLEVBQUVnQixNQUFNTyxPQUFPLENBQUMsQ0FBQztJQUNuRDtJQUVBLGlCQUFpQjtJQUNqQixNQUFNLEVBQUVSLE1BQU0sRUFBRVMsU0FBUyxFQUFFLEVBQUUsR0FBR3ZCLFNBQVNnQixPQUFPLENBQzdDQyxJQUFJLENBQUMsVUFDTE8sWUFBWSxDQUFDZjtJQUVoQixPQUFPYztBQUNUO0FBRU8sZUFBZUUsaUJBQWlCaEIsUUFBZ0I7SUFDckQsTUFBTSxFQUFFTSxLQUFLLEVBQUUsR0FBRyxNQUFNZixTQUFTZ0IsT0FBTyxDQUNyQ0MsSUFBSSxDQUFDLFVBQ0xTLE1BQU0sQ0FBQztRQUFDakI7S0FBUztJQUVwQixJQUFJTSxPQUFPO1FBQ1QsTUFBTSxJQUFJaEIsTUFBTSxDQUFDLGVBQWUsRUFBRWdCLE1BQU1PLE9BQU8sQ0FBQyxDQUFDO0lBQ25EO0FBQ0Y7QUFFTyxTQUFTSyxpQkFBaUJsQixRQUFnQjtJQUMvQyxNQUFNLEVBQUVLLE1BQU0sRUFBRVMsU0FBUyxFQUFFLEVBQUUsR0FBR3ZCLFNBQVNnQixPQUFPLENBQzdDQyxJQUFJLENBQUMsVUFDTE8sWUFBWSxDQUFDZjtJQUVoQixPQUFPYztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmxhY2stYmxvZy8uL3NyYy9saWIvc3VwYWJhc2UudHM/MDZlMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3VwYWJhc2UtanMnXG5cbmNvbnN0IHN1cGFiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIVxuY29uc3Qgc3VwYWJhc2VBbm9uS2V5ID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkhXG5cbmlmICghc3VwYWJhc2VVcmwgfHwgIXN1cGFiYXNlQW5vbktleSkge1xuICB0aHJvdyBuZXcgRXJyb3IoJ01pc3NpbmcgU3VwYWJhc2UgZW52aXJvbm1lbnQgdmFyaWFibGVzJylcbn1cblxuZXhwb3J0IGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUFub25LZXkpXG5cbi8vIEZpbGUgdXBsb2FkIHV0aWxpdGllc1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHVwbG9hZENvdmVySW1hZ2UoZmlsZTogRmlsZSwgbm92ZWxJZDogc3RyaW5nKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgY29uc3QgZmlsZUV4dCA9IGZpbGUubmFtZS5zcGxpdCgnLicpLnBvcCgpPy50b0xvd2VyQ2FzZSgpXG4gIGNvbnN0IGZpbGVOYW1lID0gYCR7bm92ZWxJZH0uJHtmaWxlRXh0fWBcbiAgXG4gIC8vIFZhbGlkYXRlIGZpbGUgdHlwZVxuICBjb25zdCBhbGxvd2VkVHlwZXMgPSBbJ2pwZycsICdqcGVnJywgJ3BuZycsICd3ZWJwJ11cbiAgaWYgKCFmaWxlRXh0IHx8ICFhbGxvd2VkVHlwZXMuaW5jbHVkZXMoZmlsZUV4dCkpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0ludmFsaWQgZmlsZSB0eXBlLiBPbmx5IEpQRywgUE5HLCBhbmQgV2ViUCBmaWxlcyBhcmUgYWxsb3dlZC4nKVxuICB9XG4gIFxuICAvLyBWYWxpZGF0ZSBmaWxlIHNpemUgKG1heCA1TUIpXG4gIGNvbnN0IG1heFNpemUgPSA1ICogMTAyNCAqIDEwMjQgLy8gNU1CIGluIGJ5dGVzXG4gIGlmIChmaWxlLnNpemUgPiBtYXhTaXplKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdGaWxlIHNpemUgdG9vIGxhcmdlLiBNYXhpbXVtIHNpemUgaXMgNU1CLicpXG4gIH1cbiAgXG4gIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLnN0b3JhZ2VcbiAgICAuZnJvbSgnY292ZXJzJylcbiAgICAudXBsb2FkKGZpbGVOYW1lLCBmaWxlLCB7IFxuICAgICAgdXBzZXJ0OiB0cnVlLFxuICAgICAgY29udGVudFR5cGU6IGZpbGUudHlwZSBcbiAgICB9KVxuICAgIFxuICBpZiAoZXJyb3IpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoYFVwbG9hZCBmYWlsZWQ6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICB9XG4gIFxuICAvLyBHZXQgcHVibGljIFVSTFxuICBjb25zdCB7IGRhdGE6IHsgcHVibGljVXJsIH0gfSA9IHN1cGFiYXNlLnN0b3JhZ2VcbiAgICAuZnJvbSgnY292ZXJzJylcbiAgICAuZ2V0UHVibGljVXJsKGZpbGVOYW1lKVxuICAgIFxuICByZXR1cm4gcHVibGljVXJsXG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBkZWxldGVDb3ZlckltYWdlKGZpbGVOYW1lOiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+IHtcbiAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2Uuc3RvcmFnZVxuICAgIC5mcm9tKCdjb3ZlcnMnKVxuICAgIC5yZW1vdmUoW2ZpbGVOYW1lXSlcbiAgICBcbiAgaWYgKGVycm9yKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKGBEZWxldGUgZmFpbGVkOiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0Q292ZXJJbWFnZVVybChmaWxlTmFtZTogc3RyaW5nKTogc3RyaW5nIHtcbiAgY29uc3QgeyBkYXRhOiB7IHB1YmxpY1VybCB9IH0gPSBzdXBhYmFzZS5zdG9yYWdlXG4gICAgLmZyb20oJ2NvdmVycycpXG4gICAgLmdldFB1YmxpY1VybChmaWxlTmFtZSlcbiAgICBcbiAgcmV0dXJuIHB1YmxpY1VybFxufVxuIl0sIm5hbWVzIjpbImNyZWF0ZUNsaWVudCIsInN1cGFiYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsInN1cGFiYXNlQW5vbktleSIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwiRXJyb3IiLCJzdXBhYmFzZSIsInVwbG9hZENvdmVySW1hZ2UiLCJmaWxlIiwibm92ZWxJZCIsImZpbGVFeHQiLCJuYW1lIiwic3BsaXQiLCJwb3AiLCJ0b0xvd2VyQ2FzZSIsImZpbGVOYW1lIiwiYWxsb3dlZFR5cGVzIiwiaW5jbHVkZXMiLCJtYXhTaXplIiwic2l6ZSIsImRhdGEiLCJlcnJvciIsInN0b3JhZ2UiLCJmcm9tIiwidXBsb2FkIiwidXBzZXJ0IiwiY29udGVudFR5cGUiLCJ0eXBlIiwibWVzc2FnZSIsInB1YmxpY1VybCIsImdldFB1YmxpY1VybCIsImRlbGV0ZUNvdmVySW1hZ2UiLCJyZW1vdmUiLCJnZXRDb3ZlckltYWdlVXJsIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/jose","vendor-chunks/@supabase","vendor-chunks/openid-client","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/oauth","vendor-chunks/@panva","vendor-chunks/isows","vendor-chunks/yallist","vendor-chunks/tr46","vendor-chunks/preact-render-to-string","vendor-chunks/oidc-token-hash","vendor-chunks/webidl-conversions","vendor-chunks/preact","vendor-chunks/cookie","vendor-chunks/@next-auth"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnovels%2F%5Bid%5D%2Froute&page=%2Fapi%2Fnovels%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnovels%2F%5Bid%5D%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();