import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { supabase } from "@/lib/supabase"
import { createId } from "@paralleldrive/cuid2"
import { z } from "zod"

const createCreditPackageSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().min(1).max(500),
  credits: z.number().int().min(1).max(10000),
  bonusCredits: z.number().int().min(0).max(5000).default(0),
  price: z.number().min(0.01).max(1000),
  currency: z.string().length(3).default("usd"),
  sortOrder: z.number().int().min(1).max(100).default(1),
  isActive: z.boolean().default(true),
  isPopular: z.boolean().default(false),
  isBestValue: z.boolean().default(false),
})

const updateCreditPackageSchema = createCreditPackageSchema.partial()

// GET /api/admin/credit-packages - Get all credit packages (admin only)
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const includeInactive = searchParams.get('includeInactive') === 'true'

    // Get credit packages using Supabase
    let packagesQuery = supabase
      .from('credit_packages')
      .select('*')
      .order('sort_order', { ascending: true })

    if (!includeInactive) {
      packagesQuery = packagesQuery.eq('is_active', true)
    }

    const { data: packages, error: packagesError } = await packagesQuery

    if (packagesError) {
      console.error("Error fetching credit packages:", packagesError)
      return NextResponse.json({ error: "Failed to fetch credit packages" }, { status: 500 })
    }

    // Get purchase counts and revenue for each package
    const packagesWithMetrics = await Promise.all(
      (packages || []).map(async (pkg) => {
        // Get purchase count
        const { count: purchaseCount } = await supabase
          .from('credit_purchases')
          .select('id', { count: 'exact', head: true })
          .eq('package_id', pkg.id)

        // Get total revenue
        const { data: revenueData } = await supabase
          .from('credit_purchases')
          .select('amount')
          .eq('package_id', pkg.id)
          .eq('status', 'COMPLETED')

        const totalRevenue = revenueData?.reduce((sum, purchase) => sum + (purchase.amount || 0), 0) || 0

        // Transform to match expected format
        return {
          id: pkg.id,
          name: pkg.name,
          description: pkg.description,
          credits: pkg.credits,
          price: pkg.price,
          currency: pkg.currency,
          bonusCredits: pkg.bonus_credits,
          isActive: pkg.is_active,
          sortOrder: pkg.sort_order,
          stripePriceId: pkg.stripe_price_id,
          createdAt: pkg.created_at,
          updatedAt: pkg.updated_at,
          totalPurchases: purchaseCount || 0,
          totalRevenue,
          valuePerCredit: pkg.price / (pkg.credits + pkg.bonus_credits),
          bonusPercentage: pkg.bonus_credits > 0 ? Math.round((pkg.bonus_credits / pkg.credits) * 100) : 0
        }
      })
    )

    return NextResponse.json({ packages: packagesWithMetrics })
  } catch (error) {
    console.error("Error fetching credit packages:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/admin/credit-packages - Create new credit package (admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const packageData = createCreditPackageSchema.parse(body)

    // Check if name already exists using Supabase
    const { data: existingPackage } = await supabase
      .from('credit_packages')
      .select('id')
      .eq('name', packageData.name)
      .single()

    if (existingPackage) {
      return NextResponse.json(
        { error: "Package name already exists" },
        { status: 400 }
      )
    }

    // If this is marked as popular or best value, unmark others
    if (packageData.isPopular) {
      await supabase
        .from('credit_packages')
        .update({ is_popular: false })
        .eq('is_popular', true)
    }

    if (packageData.isBestValue) {
      await supabase
        .from('credit_packages')
        .update({ is_best_value: false })
        .eq('is_best_value', true)
    }

    // Create new credit package using Supabase
    const now = new Date().toISOString()
    const { data: creditPackage, error: createError } = await supabase
      .from('credit_packages')
      .insert({
        id: createId(),
        name: packageData.name,
        description: packageData.description,
        credits: packageData.credits,
        price: packageData.price,
        currency: packageData.currency,
        bonus_credits: packageData.bonusCredits,
        is_active: packageData.isActive,
        sort_order: packageData.sortOrder,
        is_popular: packageData.isPopular || false,
        is_best_value: packageData.isBestValue || false,
        created_at: now,
        updated_at: now,
      })
      .select('*')
      .single()

    if (createError) {
      console.error("Error creating credit package:", createError)
      return NextResponse.json({ error: "Failed to create credit package" }, { status: 500 })
    }

    // Transform to match expected format
    const transformedPackage = {
      id: creditPackage.id,
      name: creditPackage.name,
      description: creditPackage.description,
      credits: creditPackage.credits,
      price: creditPackage.price,
      currency: creditPackage.currency,
      bonusCredits: creditPackage.bonus_credits,
      isActive: creditPackage.is_active,
      sortOrder: creditPackage.sort_order,
      stripePriceId: creditPackage.stripe_price_id,
      createdAt: creditPackage.created_at,
      updatedAt: creditPackage.updated_at,
    }

    return NextResponse.json({ package: transformedPackage }, { status: 201 })
  } catch (error) {
    console.error("Error creating credit package:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// PUT /api/admin/credit-packages - Bulk update package order
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { packages } = z.object({
      packages: z.array(z.object({
        id: z.string(),
        sortOrder: z.number().int().min(1)
      }))
    }).parse(body)

    // Update sort orders in a transaction
    await prisma.$transaction(
      packages.map(pkg => 
        prisma.creditPackage.update({
          where: { id: pkg.id },
          data: { sortOrder: pkg.sortOrder }
        })
      )
    )

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error updating package order:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
