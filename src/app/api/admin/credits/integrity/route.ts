import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { supabase } from "@/lib/supabase"
import { createId } from "@paralleldrive/cuid2"
import { CreditTransactionManager } from "@/lib/database/credit-transaction-manager"

// GET /api/admin/credits/integrity - Check credit transaction integrity (admin only)
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const autoFix = searchParams.get('autoFix') === 'true'

    if (userId) {
      // Check specific user
      const result = await CreditTransactionManager.validateTransactionIntegrity(userId)
      
      if (!result.isValid && autoFix) {
        // Auto-fix the discrepancy using Supabase
        const { error: updateError } = await supabase
          .from('users')
          .update({ credit_balance: result.expectedBalance })
          .eq('id', userId)

        if (updateError) {
          console.error("Error updating user credit balance:", updateError)
        }

        // Log the fix using Supabase
        const { error: logError } = await supabase
          .from('credit_transactions')
          .insert({
            id: createId(),
            user_id: userId,
            type: 'CREDIT',
            status: 'COMPLETED',
            amount: result.discrepancy,
            description: `Balance correction: Fixed discrepancy of ${result.discrepancy} credits`,
            source_type: 'system_correction',
            source_id: 'integrity_check',
            balance_before: result.actualBalance,
            balance_after: result.expectedBalance,
            metadata: {
              type: 'integrity_fix',
              originalDiscrepancy: result.discrepancy,
              fixedAt: new Date().toISOString()
            },
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })

        if (logError) {
          console.error("Error logging integrity fix:", logError)
        }

        return NextResponse.json({
          userId,
          wasFixed: true,
          ...result,
          newBalance: result.expectedBalance
        })
      }

      return NextResponse.json({
        userId,
        wasFixed: false,
        ...result
      })
    }

    // Check all users with credit balances using Supabase
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, credit_balance')
      .gt('credit_balance', 0)
      .limit(100) // Limit to prevent timeout

    if (usersError) {
      console.error("Error fetching users:", usersError)
      return NextResponse.json({ error: "Failed to fetch users" }, { status: 500 })
    }

    const results = []
    let totalDiscrepancies = 0
    let usersWithIssues = 0

    for (const user of users || []) {
      try {
        const integrity = await CreditTransactionManager.validateTransactionIntegrity(user.id)
        
        if (!integrity.isValid) {
          usersWithIssues++
          totalDiscrepancies += Math.abs(integrity.discrepancy)
          
          if (autoFix) {
            // Auto-fix the discrepancy using Supabase
            const { error: updateError } = await supabase
              .from('users')
              .update({ credit_balance: integrity.expectedBalance })
              .eq('id', user.id)

            if (updateError) {
              console.error("Error updating user credit balance:", updateError)
            }

            // Log the fix using Supabase
            const { error: logError } = await supabase
              .from('credit_transactions')
              .insert({
                id: createId(),
                user_id: user.id,
                type: integrity.discrepancy > 0 ? 'CREDIT' : 'DEBIT',
                status: 'COMPLETED',
                amount: integrity.discrepancy,
                description: `Balance correction: Fixed discrepancy of ${integrity.discrepancy} credits`,
                source_type: 'system_correction',
                source_id: 'bulk_integrity_check',
                balance_before: integrity.actualBalance,
                balance_after: integrity.expectedBalance,
                metadata: {
                  type: 'integrity_fix',
                  originalDiscrepancy: integrity.discrepancy,
                  fixedAt: new Date().toISOString()
                },
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
              })

            if (logError) {
              console.error("Error logging integrity fix:", logError)
            }
          }
        }

        results.push({
          userId: user.id,
          email: user.email,
          ...integrity,
          wasFixed: !integrity.isValid && autoFix
        })
      } catch (error) {
        results.push({
          userId: user.id,
          email: user.email,
          error: error instanceof Error ? error.message : 'Unknown error',
          isValid: false
        })
      }
    }

    return NextResponse.json({
      summary: {
        totalUsersChecked: users?.length || 0,
        usersWithIssues,
        totalDiscrepancies,
        autoFixApplied: autoFix
      },
      results: results.filter(r => !r.isValid || r.error) // Only return problematic users
    })

  } catch (error) {
    console.error("Error checking credit integrity:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/admin/credits/integrity - Fix credit balance discrepancies (admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { userIds, action } = body

    if (!Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json(
        { error: "userIds array is required" },
        { status: 400 }
      )
    }

    if (action !== 'fix_discrepancies') {
      return NextResponse.json(
        { error: "Invalid action. Only 'fix_discrepancies' is supported" },
        { status: 400 }
      )
    }

    const results = []

    for (const userId of userIds) {
      try {
        const integrity = await CreditTransactionManager.validateTransactionIntegrity(userId)
        
        if (!integrity.isValid) {
          // Fix the discrepancy using Supabase
          const { error: updateError } = await supabase
            .from('users')
            .update({ credit_balance: integrity.expectedBalance })
            .eq('id', userId)

          if (updateError) {
            console.error("Error updating user credit balance:", updateError)
            throw updateError
          }

          // Log the fix using Supabase
          const { error: logError } = await supabase
            .from('credit_transactions')
            .insert({
              id: createId(),
              user_id: userId,
              type: integrity.discrepancy > 0 ? 'CREDIT' : 'DEBIT',
              status: 'COMPLETED',
              amount: integrity.discrepancy,
              description: `Manual balance correction: Fixed discrepancy of ${integrity.discrepancy} credits`,
              source_type: 'admin_correction',
              source_id: session.user.id,
              balance_before: integrity.actualBalance,
              balance_after: integrity.expectedBalance,
              metadata: {
                type: 'manual_integrity_fix',
                adminUserId: session.user.id,
                originalDiscrepancy: integrity.discrepancy,
                fixedAt: new Date().toISOString()
              },
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            })

          if (logError) {
            console.error("Error logging integrity fix:", logError)
            throw logError
          }

          results.push({
            userId,
            success: true,
            discrepancyFixed: integrity.discrepancy,
            newBalance: integrity.expectedBalance
          })
        } else {
          results.push({
            userId,
            success: true,
            message: 'No discrepancy found'
          })
        }
      } catch (error) {
        results.push({
          userId,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return NextResponse.json({
      message: "Integrity fix completed",
      results
    })

  } catch (error) {
    console.error("Error fixing credit integrity:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
