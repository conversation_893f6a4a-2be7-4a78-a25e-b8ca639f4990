import { NextRequest, NextResponse } from "next/server"
import { supabase } from "@/lib/supabase"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { id } = params

  try {
    // Get author information using Supabase
    const { data: author, error: authorError } = await supabase
      .from('users')
      .select('id, name, image, bio, created_at')
      .eq('id', id)
      .eq('role', 'AUTHOR')
      .single()

    if (authorError || !author) {
      return NextResponse.json({ error: "Author not found" }, { status: 404 })
    }

    // Get published novels for this author
    const { data: novels, error: novelsError } = await supabase
      .from('novels')
      .select('id, title, description, synopsis, genre, tags, status, published_at, created_at, updated_at')
      .eq('author_id', id)
      .eq('status', 'PUBLISHED')
      .order('published_at', { ascending: false })

    if (novelsError) {
      console.error("Error fetching novels:", novelsError)
      return NextResponse.json({ error: "Failed to fetch novels" }, { status: 500 })
    }

    // Get chapter counts for each novel
    const novelIds = novels?.map(novel => novel.id) || []
    let chapterCounts: { [key: string]: number } = {}

    if (novelIds.length > 0) {
      const { data: chapters } = await supabase
        .from('chapters')
        .select('novel_id')
        .in('novel_id', novelIds)
        .eq('status', 'PUBLISHED')

      // Count chapters per novel
      chapters?.forEach(chapter => {
        chapterCounts[chapter.novel_id] = (chapterCounts[chapter.novel_id] || 0) + 1
      })
    }

    // Calculate total chapters across all novels
    const totalChapters = Object.values(chapterCounts).reduce((sum, count) => sum + count, 0)

    // Transform novels to match expected format
    const transformedNovels = novels?.map(novel => ({
      id: novel.id,
      title: novel.title,
      description: novel.description,
      synopsis: novel.synopsis,
      genre: novel.genre,
      tags: novel.tags,
      status: novel.status,
      publishedAt: novel.published_at,
      createdAt: novel.created_at,
      updatedAt: novel.updated_at,
      _count: {
        chapters: chapterCounts[novel.id] || 0
      }
    })) || []

    const authorProfile = {
      id: author.id,
      name: author.name,
      image: author.image,
      bio: author.bio,
      createdAt: author.created_at,
      novels: transformedNovels,
      _count: {
        novels: transformedNovels.length
      },
      stats: {
        totalNovels: transformedNovels.length,
        totalChapters,
        memberSince: author.created_at
      }
    }

    return NextResponse.json(authorProfile)
  } catch (error) {
    console.error("Error fetching author profile:", error)
    return NextResponse.json(
      { error: "Failed to fetch author profile" },
      { status: 500 }
    )
  }
}
