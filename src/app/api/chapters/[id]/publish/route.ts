import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { supabase } from "@/lib/supabase"

interface RouteParams {
  params: {
    id: string
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: RouteParams
) {
  const { id } = params

  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== "AUTHOR") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get chapter and verify ownership using Supabase
    const { data: chapter, error: chapterError } = await supabase
      .from('chapters')
      .select(`
        *,
        novels!chapters_novel_id_fkey(id, title, author_id, status)
      `)
      .eq('id', id)
      .single()

    if (chapterError || !chapter) {
      return NextResponse.json({ error: "Chapter not found" }, { status: 404 })
    }

    if (chapter.novels.author_id !== session.user.id) {
      return NextResponse.json(
        { error: "You don't have permission to edit this chapter" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { status } = body

    // Validate status
    if (!status || !Object.values(ChapterStatus).includes(status)) {
      return NextResponse.json(
        { error: "Invalid status. Must be DRAFT or PUBLISHED." },
        { status: 400 }
      )
    }

    // Validate chapter content before publishing
    if (status === ChapterStatus.PUBLISHED) {
      if (!chapter.title?.trim() || !chapter.content?.trim()) {
        return NextResponse.json(
          { error: "Cannot publish chapter with empty title or content" },
          { status: 400 }
        )
      }
    }

    // Update chapter status using Supabase
    const updateData: any = { status }
    if (status === "PUBLISHED") {
      updateData.updated_at = new Date().toISOString()
    }

    const { data: updatedChapter, error: updateError } = await supabase
      .from('chapters')
      .update(updateData)
      .eq('id', id)
      .select(`
        *,
        novels!chapters_novel_id_fkey(id, title, author_id, status)
      `)
      .single()

    if (updateError) {
      console.error("Error updating chapter status:", updateError)
      return NextResponse.json({ error: "Failed to update chapter status" }, { status: 500 })
    }

    const actionMessage = status === "PUBLISHED"
      ? "Chapter published successfully"
      : "Chapter unpublished successfully"

    // Transform chapter to match expected format
    const transformedChapter = {
      id: updatedChapter.id,
      title: updatedChapter.title,
      content: updatedChapter.content,
      order: updatedChapter.order,
      status: updatedChapter.status,
      novelId: updatedChapter.novel_id,
      createdAt: updatedChapter.created_at,
      updatedAt: updatedChapter.updated_at,
      novel: {
        id: updatedChapter.novels.id,
        title: updatedChapter.novels.title,
        authorId: updatedChapter.novels.author_id,
        status: updatedChapter.novels.status,
      },
    }

    return NextResponse.json({
      success: true,
      data: transformedChapter,
      message: actionMessage,
    })
  } catch (error) {
    console.error("Error updating chapter status:", error)
    return NextResponse.json(
      { error: "Failed to update chapter status" },
      { status: 500 }
    )
  }
}

// Batch update multiple chapters' publish status
export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== "AUTHOR") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { chapterIds, status } = body

    // Validate inputs
    if (!Array.isArray(chapterIds) || chapterIds.length === 0) {
      return NextResponse.json(
        { error: "Chapter IDs array is required" },
        { status: 400 }
      )
    }

    if (!status || !["DRAFT", "PUBLISHED"].includes(status)) {
      return NextResponse.json(
        { error: "Invalid status. Must be DRAFT or PUBLISHED." },
        { status: 400 }
      )
    }

    // Verify all chapters belong to the author using Supabase
    const { data: chapters, error: chaptersError } = await supabase
      .from('chapters')
      .select(`
        id,
        title,
        content,
        novels!chapters_novel_id_fkey(author_id)
      `)
      .in('id', chapterIds)

    if (chaptersError) {
      console.error("Error fetching chapters:", chaptersError)
      return NextResponse.json({ error: "Failed to fetch chapters" }, { status: 500 })
    }

    // Check if all chapters exist and belong to the author
    if (chapters.length !== chapterIds.length) {
      return NextResponse.json(
        { error: "Some chapters not found" },
        { status: 404 }
      )
    }

    const unauthorizedChapters = chapters.filter(
      chapter => chapter.novels.author_id !== session.user.id
    )

    if (unauthorizedChapters.length > 0) {
      return NextResponse.json(
        { error: "You don't have permission to edit some of these chapters" },
        { status: 403 }
      )
    }

    // Validate content for publishing
    if (status === "PUBLISHED") {
      const invalidChapters = chapters.filter(
        chapter => !chapter.title?.trim() || !chapter.content?.trim()
      )

      if (invalidChapters.length > 0) {
        return NextResponse.json(
          { error: "Cannot publish chapters with empty title or content" },
          { status: 400 }
        )
      }
    }

    // Update all chapters using Supabase
    const updateData: any = { status }
    if (status === "PUBLISHED") {
      updateData.updated_at = new Date().toISOString()
    }

    const { data: updatedChapters, error: updateError } = await supabase
      .from('chapters')
      .update(updateData)
      .in('id', chapterIds)
      .select('id')

    if (updateError) {
      console.error("Error batch updating chapter status:", updateError)
      return NextResponse.json({ error: "Failed to update chapter status" }, { status: 500 })
    }

    const updatedCount = updatedChapters?.length || 0
    const actionMessage = status === "PUBLISHED"
      ? `${updatedCount} chapters published successfully`
      : `${updatedCount} chapters unpublished successfully`

    return NextResponse.json({
      success: true,
      data: {
        updatedCount,
        status,
      },
      message: actionMessage,
    })
  } catch (error) {
    console.error("Error batch updating chapter status:", error)
    return NextResponse.json(
      { error: "Failed to update chapter status" },
      { status: 500 }
    )
  }
}
