import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"

interface RouteParams {
  params: {
    id: string
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: RouteParams
) {
  const { id } = params

  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== "AUTHOR") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get chapter and verify ownership
    const chapter = await prisma.chapter.findUnique({
      where: { id },
      include: {
        novel: {
          select: {
            id: true,
            title: true,
            authorId: true,
            status: true,
          },
        },
      },
    })

    if (!chapter) {
      return NextResponse.json({ error: "Chapter not found" }, { status: 404 })
    }

    if (chapter.novel.authorId !== session.user.id) {
      return NextResponse.json(
        { error: "You don't have permission to edit this chapter" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { status } = body

    // Validate status
    if (!status || !Object.values(ChapterStatus).includes(status)) {
      return NextResponse.json(
        { error: "Invalid status. Must be DRAFT or PUBLISHED." },
        { status: 400 }
      )
    }

    // Validate chapter content before publishing
    if (status === ChapterStatus.PUBLISHED) {
      if (!chapter.title.trim() || !chapter.content.trim()) {
        return NextResponse.json(
          { error: "Cannot publish chapter with empty title or content" },
          { status: 400 }
        )
      }
    }

    // Update chapter status
    const updatedChapter = await prisma.chapter.update({
      where: { id },
      data: {
        status: status,
        // Update timestamp when publishing
        ...(status === "PUBLISHED" && { updatedAt: new Date() })
      },
      include: {
        novel: {
          select: {
            id: true,
            title: true,
            authorId: true,
            status: true,
          },
        },
      },
    })

    const actionMessage = status === "PUBLISHED"
      ? "Chapter published successfully"
      : "Chapter unpublished successfully"

    return NextResponse.json({
      success: true,
      data: updatedChapter,
      message: actionMessage,
    })
  } catch (error) {
    console.error("Error updating chapter status:", error)
    return NextResponse.json(
      { error: "Failed to update chapter status" },
      { status: 500 }
    )
  }
}

// Batch update multiple chapters' publish status
export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== "AUTHOR") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { chapterIds, status } = body

    // Validate inputs
    if (!Array.isArray(chapterIds) || chapterIds.length === 0) {
      return NextResponse.json(
        { error: "Chapter IDs array is required" },
        { status: 400 }
      )
    }

    if (!status || !["DRAFT", "PUBLISHED"].includes(status)) {
      return NextResponse.json(
        { error: "Invalid status. Must be DRAFT or PUBLISHED." },
        { status: 400 }
      )
    }

    // Verify all chapters belong to the author
    const chapters = await prisma.chapter.findMany({
      where: {
        id: { in: chapterIds },
      },
      include: {
        novel: {
          select: {
            authorId: true,
          },
        },
      },
    })

    // Check if all chapters exist and belong to the author
    if (chapters.length !== chapterIds.length) {
      return NextResponse.json(
        { error: "Some chapters not found" },
        { status: 404 }
      )
    }

    const unauthorizedChapters = chapters.filter(
      chapter => chapter.novel.authorId !== session.user.id
    )

    if (unauthorizedChapters.length > 0) {
      return NextResponse.json(
        { error: "You don't have permission to edit some of these chapters" },
        { status: 403 }
      )
    }

    // Validate content for publishing
    if (status === "PUBLISHED") {
      const invalidChapters = chapters.filter(
        chapter => !chapter.title.trim() || !chapter.content.trim()
      )

      if (invalidChapters.length > 0) {
        return NextResponse.json(
          { error: "Cannot publish chapters with empty title or content" },
          { status: 400 }
        )
      }
    }

    // Update all chapters
    const updatedChapters = await prisma.chapter.updateMany({
      where: {
        id: { in: chapterIds },
      },
      data: {
        status: status,
        ...(status === "PUBLISHED" && { updatedAt: new Date() })
      },
    })

    const actionMessage = status === "PUBLISHED"
      ? `${updatedChapters.count} chapters published successfully`
      : `${updatedChapters.count} chapters unpublished successfully`

    return NextResponse.json({
      success: true,
      data: {
        updatedCount: updatedChapters.count,
        status,
      },
      message: actionMessage,
    })
  } catch (error) {
    console.error("Error batch updating chapter status:", error)
    return NextResponse.json(
      { error: "Failed to update chapter status" },
      { status: 500 }
    )
  }
}
