import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { supabase } from "@/lib/supabase"
import { UserRole } from "@/types/auth"
import { z } from "zod"

const earningsQuerySchema = z.object({
  page: z.string().optional().default("1"),
  limit: z.string().optional().default("10"),
  type: z.enum(['SUBSCRIPTION_REVENUE', 'TIP', 'BONUS', 'REFERRAL', 'CREDIT_PURCHASE']).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

// GET /api/earnings - Get user's earnings with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.AUTHOR) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const query = earningsQuerySchema.parse({
      page: searchParams.get('page') || "1",
      limit: searchParams.get('limit') || "10",
      type: searchParams.get('type') || undefined,
      startDate: searchParams.get('startDate') || undefined,
      endDate: searchParams.get('endDate') || undefined,
    })

    const page = parseInt(query.page)
    const limit = parseInt(query.limit)
    const offset = (page - 1) * limit

    // Build Supabase query
    let earningsQuery = supabase
      .from('earnings')
      .select(`
        *,
        payouts!earnings_payout_id_fkey(id, status, processed_at)
      `)
      .eq('user_id', session.user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    // Add type filter if specified
    if (query.type) {
      earningsQuery = earningsQuery.eq('type', query.type)
    }

    // Add date range filters if specified
    if (query.startDate) {
      earningsQuery = earningsQuery.gte('created_at', query.startDate)
    }
    if (query.endDate) {
      earningsQuery = earningsQuery.lte('created_at', query.endDate)
    }

    // Build count query separately
    let countQuery = supabase
      .from('earnings')
      .select('id', { count: 'exact', head: true })
      .eq('user_id', session.user.id)

    if (query.type) {
      countQuery = countQuery.eq('type', query.type)
    }
    if (query.startDate) {
      countQuery = countQuery.gte('created_at', query.startDate)
    }
    if (query.endDate) {
      countQuery = countQuery.lte('created_at', query.endDate)
    }

    // Get earnings and total count
    const [earningsResult, countResult] = await Promise.all([
      earningsQuery,
      countQuery
    ])

    if (earningsResult.error) {
      console.error("Error fetching earnings:", earningsResult.error)
      return NextResponse.json({ error: "Failed to fetch earnings" }, { status: 500 })
    }

    const earnings = earningsResult.data || []
    const total = countResult.count || 0

    const pages = Math.ceil(total / limit)

    // Transform earnings to match expected format
    const transformedEarnings = earnings.map(earning => ({
      id: earning.id,
      userId: earning.user_id,
      type: earning.type,
      amount: earning.amount,
      currency: earning.currency,
      description: earning.description,
      metadata: earning.metadata,
      payoutId: earning.payout_id,
      createdAt: earning.created_at,
      updatedAt: earning.updated_at,
      payout: earning.payouts ? {
        id: earning.payouts.id,
        status: earning.payouts.status,
        processedAt: earning.payouts.processed_at,
      } : null
    }))

    return NextResponse.json({
      data: transformedEarnings,
      pagination: {
        page,
        limit,
        total,
        pages,
        hasNext: page < pages,
        hasPrev: page > 1,
      }
    })
  } catch (error) {
    console.error("Error fetching earnings:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
