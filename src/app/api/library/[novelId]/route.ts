import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { supabase } from "@/lib/supabase"

export async function DELETE(
  request: NextRequest,
  { params }: { params: { novelId: string } }
) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const { novelId } = params

  try {
    // Check if the entry exists using Supabase
    const { data: existingEntry, error: findError } = await supabase
      .from('library')
      .select('id')
      .eq('user_id', session.user.id)
      .eq('novel_id', novelId)
      .single()

    if (findError || !existingEntry) {
      return NextResponse.json(
        { error: "Novel not found in library" },
        { status: 404 }
      )
    }

    // Remove from library using Supabase
    const { error: deleteError } = await supabase
      .from('library')
      .delete()
      .eq('user_id', session.user.id)
      .eq('novel_id', novelId)

    if (deleteError) {
      console.error("Error removing from library:", deleteError)
      return NextResponse.json({ error: "Failed to remove from library" }, { status: 500 })
    }

    return NextResponse.json({ message: "Novel removed from library" })
  } catch (error) {
    console.error("Error removing from library:", error)
    return NextResponse.json(
      { error: "Failed to remove from library" },
      { status: 500 }
    )
  }
}