import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { supabase } from "@/lib/supabase"
import { createId } from "@paralleldrive/cuid2"

export async function GET() {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    // Get library entries with novels and authors using Supabase
    const { data: libraryEntries, error: libraryError } = await supabase
      .from('library')
      .select(`
        *,
        novels!library_novel_id_fkey(
          *,
          users!novels_author_id_fkey(id, name, image)
        )
      `)
      .eq('user_id', session.user.id)
      .order('added_at', { ascending: false })

    if (libraryError) {
      console.error("Error fetching library:", libraryError)
      return NextResponse.json({ error: "Failed to fetch library" }, { status: 500 })
    }

    // Get chapter counts for each novel
    const novelIds = libraryEntries?.map(entry => entry.novels.id) || []
    let chapterCounts: { [key: string]: number } = {}

    if (novelIds.length > 0) {
      const { data: chapters } = await supabase
        .from('chapters')
        .select('novel_id')
        .in('novel_id', novelIds)

      // Count chapters per novel
      chapters?.forEach(chapter => {
        chapterCounts[chapter.novel_id] = (chapterCounts[chapter.novel_id] || 0) + 1
      })
    }

    // Transform to match expected format
    const transformedLibrary = libraryEntries?.map(entry => ({
      id: entry.id,
      userId: entry.user_id,
      novelId: entry.novel_id,
      addedAt: entry.added_at,
      novel: {
        id: entry.novels.id,
        title: entry.novels.title,
        description: entry.novels.description,
        synopsis: entry.novels.synopsis,
        coverImage: entry.novels.cover_image,
        status: entry.novels.status,
        genre: entry.novels.genre,
        tags: entry.novels.tags,
        authorId: entry.novels.author_id,
        createdAt: entry.novels.created_at,
        updatedAt: entry.novels.updated_at,
        publishedAt: entry.novels.published_at,
        author: entry.novels.users,
        _count: {
          chapters: chapterCounts[entry.novels.id] || 0
        }
      }
    })) || []

    return NextResponse.json(transformedLibrary)
  } catch (error) {
    console.error("Error fetching library:", error)
    return NextResponse.json(
      { error: "Failed to fetch library" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const body = await request.json()
    const { novelId } = body

    if (!novelId) {
      return NextResponse.json(
        { error: "Novel ID is required" },
        { status: 400 }
      )
    }

    // Check if novel exists and is published
    const novel = await prisma.novel.findUnique({
      where: { id: novelId },
      select: { id: true, status: true },
    })

    if (!novel) {
      return NextResponse.json({ error: "Novel not found" }, { status: 404 })
    }

    if (novel.status !== "PUBLISHED") {
      return NextResponse.json(
        { error: "Cannot add unpublished novel to library" },
        { status: 400 }
      )
    }

    // Check if already in library
    const existingEntry = await prisma.library.findUnique({
      where: {
        userId_novelId: {
          userId: session.user.id,
          novelId,
        },
      },
    })

    if (existingEntry) {
      return NextResponse.json(
        { error: "Novel already in library" },
        { status: 409 }
      )
    }

    // Add to library
    const libraryEntry = await prisma.library.create({
      data: {
        userId: session.user.id,
        novelId,
      },
      include: {
        novel: {
          include: {
            author: { select: { id: true, name: true, image: true } },
            _count: { select: { chapters: true } },
          },
        },
      },
    })

    return NextResponse.json(libraryEntry, { status: 201 })
  } catch (error) {
    console.error("Error adding to library:", error)
    return NextResponse.json(
      { error: "Failed to add to library" },
      { status: 500 }
    )
  }
}