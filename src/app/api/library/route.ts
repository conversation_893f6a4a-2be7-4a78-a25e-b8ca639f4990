import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { supabase } from "@/lib/supabase"
import { createId } from "@paralleldrive/cuid2"

export async function GET() {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    // Get library entries with novels and authors using Supabase
    const { data: libraryEntries, error: libraryError } = await supabase
      .from('library')
      .select(`
        *,
        novels!library_novel_id_fkey(
          *,
          users!novels_author_id_fkey(id, name, image)
        )
      `)
      .eq('user_id', session.user.id)
      .order('added_at', { ascending: false })

    if (libraryError) {
      console.error("Error fetching library:", libraryError)
      return NextResponse.json({ error: "Failed to fetch library" }, { status: 500 })
    }

    // Get chapter counts for each novel
    const novelIds = libraryEntries?.map(entry => entry.novels.id) || []
    let chapterCounts: { [key: string]: number } = {}

    if (novelIds.length > 0) {
      const { data: chapters } = await supabase
        .from('chapters')
        .select('novel_id')
        .in('novel_id', novelIds)

      // Count chapters per novel
      chapters?.forEach(chapter => {
        chapterCounts[chapter.novel_id] = (chapterCounts[chapter.novel_id] || 0) + 1
      })
    }

    // Transform to match expected format
    const transformedLibrary = libraryEntries?.map(entry => ({
      id: entry.id,
      userId: entry.user_id,
      novelId: entry.novel_id,
      addedAt: entry.added_at,
      novel: {
        id: entry.novels.id,
        title: entry.novels.title,
        description: entry.novels.description,
        synopsis: entry.novels.synopsis,
        coverImage: entry.novels.cover_image,
        status: entry.novels.status,
        genre: entry.novels.genre,
        tags: entry.novels.tags,
        authorId: entry.novels.author_id,
        createdAt: entry.novels.created_at,
        updatedAt: entry.novels.updated_at,
        publishedAt: entry.novels.published_at,
        author: entry.novels.users,
        _count: {
          chapters: chapterCounts[entry.novels.id] || 0
        }
      }
    })) || []

    return NextResponse.json(transformedLibrary)
  } catch (error) {
    console.error("Error fetching library:", error)
    return NextResponse.json(
      { error: "Failed to fetch library" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const body = await request.json()
    const { novelId } = body

    if (!novelId) {
      return NextResponse.json(
        { error: "Novel ID is required" },
        { status: 400 }
      )
    }

    // Check if novel exists and is published using Supabase
    const { data: novel, error: novelError } = await supabase
      .from('novels')
      .select('id, status')
      .eq('id', novelId)
      .single()

    if (novelError || !novel) {
      return NextResponse.json({ error: "Novel not found" }, { status: 404 })
    }

    if (novel.status !== "PUBLISHED") {
      return NextResponse.json(
        { error: "Cannot add unpublished novel to library" },
        { status: 400 }
      )
    }

    // Check if already in library using Supabase
    const { data: existingEntry } = await supabase
      .from('library')
      .select('id')
      .eq('user_id', session.user.id)
      .eq('novel_id', novelId)
      .single()

    if (existingEntry) {
      return NextResponse.json(
        { error: "Novel already in library" },
        { status: 409 }
      )
    }

    // Add to library using Supabase
    const libraryId = createId()
    const now = new Date().toISOString()

    const { data: libraryEntry, error: createError } = await supabase
      .from('library')
      .insert({
        id: libraryId,
        user_id: session.user.id,
        novel_id: novelId,
        added_at: now,
      })
      .select(`
        *,
        novels!library_novel_id_fkey(
          *,
          users!novels_author_id_fkey(id, name, image)
        )
      `)
      .single()

    if (createError) {
      console.error("Error creating library entry:", createError)
      return NextResponse.json({ error: "Failed to add to library" }, { status: 500 })
    }

    // Get chapter count for the novel
    const { count: chapterCount } = await supabase
      .from('chapters')
      .select('id', { count: 'exact', head: true })
      .eq('novel_id', novelId)

    // Transform to match expected format
    const transformedEntry = {
      id: libraryEntry.id,
      userId: libraryEntry.user_id,
      novelId: libraryEntry.novel_id,
      addedAt: libraryEntry.added_at,
      novel: {
        id: libraryEntry.novels.id,
        title: libraryEntry.novels.title,
        description: libraryEntry.novels.description,
        synopsis: libraryEntry.novels.synopsis,
        coverImage: libraryEntry.novels.cover_image,
        status: libraryEntry.novels.status,
        genre: libraryEntry.novels.genre,
        tags: libraryEntry.novels.tags,
        authorId: libraryEntry.novels.author_id,
        createdAt: libraryEntry.novels.created_at,
        updatedAt: libraryEntry.novels.updated_at,
        publishedAt: libraryEntry.novels.published_at,
        author: libraryEntry.novels.users,
        _count: {
          chapters: chapterCount || 0
        }
      }
    }

    return NextResponse.json(transformedEntry, { status: 201 })
  } catch (error) {
    console.error("Error adding to library:", error)
    return NextResponse.json(
      { error: "Failed to add to library" },
      { status: 500 }
    )
  }
}