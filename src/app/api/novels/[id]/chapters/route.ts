import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { supabase } from "@/lib/supabase"
import { createId } from "@paralleldrive/cuid2"

interface RouteParams {
  params: {
    id: string
  }
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  const { id: novelId } = params
  const { searchParams } = new URL(request.url)
  const includeUnpublished = searchParams.get("includeUnpublished") === "true"



  try {
    // Get novel and check permissions using Supabase
    const { data: novel, error: novelError } = await supabase
      .from('novels')
      .select('id, title, author_id, status')
      .eq('id', novelId)
      .single()

    if (novelError || !novel) {
      return NextResponse.json({ error: "Novel not found" }, { status: 404 })
    }

    // Check authentication for unpublished content
    const session = await getServerSession(authOptions)
    const isAuthor = session?.user?.id === novel.author_id

    // Determine chapter status filter
    let chapterStatusFilter: string[] = ["PUBLISHED"]

    // Authors can always see their own draft chapters
    if (isAuthor) {
      chapterStatusFilter = ["PUBLISHED", "DRAFT"]
    }

    // Only allow access to unpublished novels for authors
    if (novel.status !== "PUBLISHED" && !isAuthor) {
      return NextResponse.json({ error: "Novel not found" }, { status: 404 })
    }

    // Fetch chapters using Supabase
    let chaptersQuery = supabase
      .from('chapters')
      .select(`
        id,
        title,
        order,
        status,
        created_at,
        updated_at
        ${isAuthor ? ', content' : ''}
      `)
      .eq('novel_id', novelId)
      .in('status', chapterStatusFilter)
      .order('order', { ascending: true })

    const { data: chapters, error: chaptersError } = await chaptersQuery

    if (chaptersError) {
      console.error("Error fetching chapters:", chaptersError)
      return NextResponse.json({ error: "Failed to fetch chapters" }, { status: 500 })
    }



    // Transform chapters to match expected format
    const transformedChapters = chapters?.map(chapter => ({
      id: chapter.id,
      title: chapter.title,
      order: chapter.order,
      status: chapter.status,
      createdAt: chapter.created_at,
      updatedAt: chapter.updated_at,
      ...(isAuthor && chapter.content && { content: chapter.content }),
    })) || []

    const response = {
      novel: {
        id: novel.id,
        title: novel.title,
        status: novel.status,
      },
      chapters: transformedChapters,
      totalChapters: transformedChapters.length,
    }


    return NextResponse.json(response)
  } catch (error) {
    console.error("Error fetching chapters:", error)
    return NextResponse.json(
      { error: "Failed to fetch chapters" },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  const { id: novelId } = params

  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== "AUTHOR") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Verify novel ownership using Supabase
    const { data: novel, error: novelError } = await supabase
      .from('novels')
      .select('id, title, author_id')
      .eq('id', novelId)
      .eq('author_id', session.user.id)
      .single()

    if (novelError || !novel) {
      return NextResponse.json(
        { error: "Novel not found or you don't have permission to edit it" },
        { status: 404 }
      )
    }

    let body
    try {
      const text = await request.text()
      console.log('Request body text:', text)
      body = text ? JSON.parse(text) : {}
    } catch (error) {
      console.error('JSON parsing error:', error)
      return NextResponse.json(
        { error: "Invalid JSON in request body" },
        { status: 400 }
      )
    }
    
    if (!body || typeof body !== 'object') {
      return NextResponse.json(
        { error: "Request body must be a valid JSON object" },
        { status: 400 }
      )
    }

    const { title, content } = body

    // Validate required fields
    if (!title || typeof title !== 'string' || title.trim().length === 0) {
      return NextResponse.json(
        { error: "Title is required and must be a non-empty string" },
        { status: 400 }
      )
    }

    if (!content || typeof content !== 'string' || content.trim().length === 0) {
      return NextResponse.json(
        { error: "Content is required and must be a non-empty string" },
        { status: 400 }
      )
    }

    // Get the next order number using Supabase
    const { data: lastChapter } = await supabase
      .from('chapters')
      .select('order')
      .eq('novel_id', novelId)
      .order('order', { ascending: false })
      .limit(1)
      .single()

    const nextOrder = (lastChapter?.order || 0) + 1

    // Create chapter using Supabase
    const chapterId = createId()
    const now = new Date().toISOString()
    const { data: chapter, error: createError } = await supabase
      .from('chapters')
      .insert({
        id: chapterId,
        title: title.trim(),
        content: content.trim(),
        order: nextOrder,
        novel_id: novelId,
        status: "DRAFT",
        created_at: now,
        updated_at: now,
      })
      .select(`
        *,
        novels!chapters_novel_id_fkey(id, title, author_id)
      `)
      .single()

    if (createError) {
      console.error("Error creating chapter:", createError)
      return NextResponse.json({ error: "Failed to create chapter" }, { status: 500 })
    }

    // Transform chapter to match expected format
    const transformedChapter = {
      id: chapter.id,
      title: chapter.title,
      content: chapter.content,
      order: chapter.order,
      status: chapter.status,
      novelId: chapter.novel_id,
      createdAt: chapter.created_at,
      updatedAt: chapter.updated_at,
      novel: {
        id: chapter.novels.id,
        title: chapter.novels.title,
        authorId: chapter.novels.author_id,
      },
    }

    return NextResponse.json({
      success: true,
      data: transformedChapter,
      message: "Chapter created successfully",
    })
  } catch (error) {
    console.error("Error creating chapter:", error)
    return NextResponse.json(
      { error: "Failed to create chapter" },
      { status: 500 }
    )
  }
}
