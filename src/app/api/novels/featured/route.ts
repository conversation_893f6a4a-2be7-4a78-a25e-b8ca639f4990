import { NextRequest, NextResponse } from "next/server"
import { supabase } from "@/lib/supabase"

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const limit = parseInt(searchParams.get("limit") || "6")
  const offset = parseInt(searchParams.get("offset") || "0")

  try {
    // For now, we'll feature novels based on:
    // 1. Published status
    // 2. Has cover image
    // 3. Has at least one published chapter
    // 4. Recently updated
    // 5. Has description/synopsis

    // First, get novels that meet basic criteria using Supabase
    const { data: novels, error: novelsError } = await supabase
      .from('novels')
      .select(`
        *,
        users!novels_author_id_fkey(id, name, image)
      `)
      .eq('status', 'PUBLISHED')
      .not('cover_image', 'is', null)
      .or('description.not.is.null,synopsis.not.is.null')
      .order('updated_at', { ascending: false })
      .limit(limit + 10) // Get extra to filter out novels without published chapters

    if (novelsError) {
      console.error("Error fetching novels:", novelsError)
      return NextResponse.json({ error: "Failed to fetch featured novels" }, { status: 500 })
    }

    // Filter novels that have at least one published chapter and get chapter counts
    const featuredNovels = []

    if (novels && novels.length > 0) {
      const novelIds = novels.map(novel => novel.id)

      // Get published chapter counts for each novel
      const { data: chapterCounts } = await supabase
        .from('chapters')
        .select('novel_id')
        .in('novel_id', novelIds)
        .eq('status', 'PUBLISHED')

      // Count chapters per novel
      const chapterCountMap: { [key: string]: number } = {}
      chapterCounts?.forEach(chapter => {
        chapterCountMap[chapter.novel_id] = (chapterCountMap[chapter.novel_id] || 0) + 1
      })

      // Filter novels that have published chapters and transform data
      for (const novel of novels) {
        const chapterCount = chapterCountMap[novel.id] || 0
        if (chapterCount > 0) {
          featuredNovels.push({
            id: novel.id,
            title: novel.title,
            description: novel.description,
            synopsis: novel.synopsis,
            coverImage: novel.cover_image,
            status: novel.status,
            genre: novel.genre,
            tags: novel.tags,
            authorId: novel.author_id,
            createdAt: novel.created_at,
            updatedAt: novel.updated_at,
            publishedAt: novel.published_at,
            author: novel.users,
            _count: {
              chapters: chapterCount
            }
          })
        }
      }

      // Sort by chapter count (desc) then by updated date (desc)
      featuredNovels.sort((a, b) => {
        if (b._count.chapters !== a._count.chapters) {
          return b._count.chapters - a._count.chapters
        }
        return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      })

      // Apply pagination
      featuredNovels.splice(limit + offset)
      featuredNovels.splice(0, offset)
    }

    // Get total count for pagination using Supabase
    const { count: totalCount, error: countError } = await supabase
      .from('novels')
      .select('id', { count: 'exact', head: true })
      .eq('status', 'PUBLISHED')
      .not('cover_image', 'is', null)
      .or('description.not.is.null,synopsis.not.is.null')

    if (countError) {
      console.error("Error counting novels:", countError)
    }

    // Transform the data to include additional metadata
    const transformedNovels = featuredNovels.map(novel => ({
      ...novel,
      publishedChapters: novel._count.chapters,
      isFeatured: true,
      featuredReason: "Popular and well-maintained",
    }))

    return NextResponse.json({
      novels: transformedNovels,
      pagination: {
        limit,
        offset,
        total: totalCount || 0,
        hasMore: offset + limit < (totalCount || 0),
      },
      metadata: {
        totalFeatured: totalCount || 0,
        criteria: [
          "Published status",
          "Has cover image",
          "Has published chapters",
          "Has description or synopsis",
          "Recently updated",
        ],
      },
    })
  } catch (error) {
    console.error("Error fetching featured novels:", error)
    return NextResponse.json(
      { error: "Failed to fetch featured novels" },
      { status: 500 }
    )
  }
}

// Admin endpoint to manually feature/unfeature novels
export async function POST(request: NextRequest) {
  try {
    // For now, this is a placeholder for future admin functionality
    // In a real implementation, you might want to add a "featured" field to the Novel model
    // and allow admins to manually curate featured content
    
    return NextResponse.json(
      { error: "Manual featuring not implemented yet" },
      { status: 501 }
    )
  } catch (error) {
    console.error("Error featuring novel:", error)
    return NextResponse.json(
      { error: "Failed to feature novel" },
      { status: 500 }
    )
  }
}
