import { NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/db"

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const limit = parseInt(searchParams.get("limit") || "6")
  const offset = parseInt(searchParams.get("offset") || "0")

  try {
    // For now, we'll feature novels based on:
    // 1. Published status
    // 2. Has cover image
    // 3. Has at least one published chapter
    // 4. Recently updated
    // 5. Has description/synopsis
    
    const featuredNovels = await prisma.novel.findMany({
      where: {
        status: "PUBLISHED",
        coverImage: {
          not: null,
        },
        chapters: {
          some: {
            status: "PUBLISHED",
          },
        },
        OR: [
          {
            description: {
              not: null,
            },
          },
          {
            synopsis: {
              not: null,
            },
          },
        ],
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        _count: {
          select: {
            chapters: {
              where: {
                status: "PUBLISHED",
              },
            },
          },
        },
      },
      orderBy: [
        // Prioritize novels with more published chapters
        {
          chapters: {
            _count: "desc",
          },
        },
        // Then by recent updates
        {
          updatedAt: "desc",
        },
      ],
      take: limit,
      skip: offset,
    })

    // Get total count for pagination
    const totalCount = await prisma.novel.count({
      where: {
        status: "PUBLISHED",
        coverImage: {
          not: null,
        },
        chapters: {
          some: {
            status: "PUBLISHED",
          },
        },
        OR: [
          {
            description: {
              not: null,
            },
          },
          {
            synopsis: {
              not: null,
            },
          },
        ],
      },
    })

    // Transform the data to include additional metadata
    const transformedNovels = featuredNovels.map(novel => ({
      ...novel,
      publishedChapters: novel._count.chapters,
      isFeatured: true,
      featuredReason: "Popular and well-maintained",
    }))

    return NextResponse.json({
      novels: transformedNovels,
      pagination: {
        limit,
        offset,
        total: totalCount,
        hasMore: offset + limit < totalCount,
      },
      metadata: {
        totalFeatured: totalCount,
        criteria: [
          "Published status",
          "Has cover image",
          "Has published chapters",
          "Has description or synopsis",
          "Recently updated",
        ],
      },
    })
  } catch (error) {
    console.error("Error fetching featured novels:", error)
    return NextResponse.json(
      { error: "Failed to fetch featured novels" },
      { status: 500 }
    )
  }
}

// Admin endpoint to manually feature/unfeature novels
export async function POST(request: NextRequest) {
  try {
    // For now, this is a placeholder for future admin functionality
    // In a real implementation, you might want to add a "featured" field to the Novel model
    // and allow admins to manually curate featured content
    
    return NextResponse.json(
      { error: "Manual featuring not implemented yet" },
      { status: 501 }
    )
  } catch (error) {
    console.error("Error featuring novel:", error)
    return NextResponse.json(
      { error: "Failed to feature novel" },
      { status: 500 }
    )
  }
}
