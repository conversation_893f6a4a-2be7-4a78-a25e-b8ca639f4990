import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { supabase } from "@/lib/supabase"
import { createId } from "@paralleldrive/cuid2"
import { z } from "zod"

const readingSessionSchema = z.object({
  novelId: z.string(),
  chapterId: z.string(),
  session: z.object({
    id: z.string(),
    startTime: z.string().transform(str => new Date(str)),
    endTime: z.string().transform(str => new Date(str)).optional(),
    duration: z.number().int().min(0),
    chaptersRead: z.array(z.string()),
    wordsRead: z.number().int().min(0),
    averageReadingSpeed: z.number().min(0),
    breaks: z.number().int().min(0),
    focusScore: z.number().min(0).max(100),
  })
})

const querySchema = z.object({
  novelId: z.string().optional(),
  limit: z.string().optional().default("10"),
  offset: z.string().optional().default("0"),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

// GET /api/reading-sessions - Get user's reading sessions
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const query = querySchema.parse({
      novelId: searchParams.get("novelId") || undefined,
      limit: searchParams.get("limit") || "10",
      offset: searchParams.get("offset") || "0",
      startDate: searchParams.get("startDate") || undefined,
      endDate: searchParams.get("endDate") || undefined,
    })

    const limit = parseInt(query.limit)
    const offset = parseInt(query.offset)

    // Build where clause
    const where: any = {
      userId: session.user.id,
    }

    if (query.novelId) {
      where.novelId = query.novelId
    }

    if (query.startDate || query.endDate) {
      where.createdAt = {}
      if (query.startDate) {
        where.createdAt.gte = new Date(query.startDate)
      }
      if (query.endDate) {
        where.createdAt.lte = new Date(query.endDate)
      }
    }

    // Get reading sessions from reading progress (we'll extend this model)
    const sessions = await prisma.readingProgress.findMany({
      where,
      include: {
        novel: {
          select: {
            id: true,
            title: true,
            coverImage: true,
          }
        },
        chapter: {
          select: {
            id: true,
            title: true,
            order: true,
          }
        }
      },
      orderBy: {
        lastReadAt: "desc",
      },
      take: limit,
      skip: offset,
    })

    // Transform to session format (simplified for now)
    const formattedSessions = sessions.map(progress => ({
      id: progress.id,
      startTime: progress.createdAt,
      endTime: progress.lastReadAt,
      duration: progress.totalTimeRead,
      chaptersRead: [progress.chapterId].filter(Boolean),
      wordsRead: Math.floor(progress.totalTimeRead * 3.5), // Estimate: ~3.5 words per second
      averageReadingSpeed: progress.totalTimeRead > 0 ? Math.round((progress.totalTimeRead * 3.5) / (progress.totalTimeRead / 60)) : 0,
      breaks: Math.floor(progress.totalTimeRead / 1800), // Estimate breaks every 30 minutes
      focusScore: Math.max(60, 100 - Math.floor(progress.totalTimeRead / 1800) * 5), // Decrease focus with breaks
      novel: progress.novel,
      chapter: progress.chapter,
      progress: progress.progress,
    }))

    return NextResponse.json({
      sessions: formattedSessions,
      pagination: {
        limit,
        offset,
        total: sessions.length,
      }
    })

  } catch (error) {
    console.error("Error fetching reading sessions:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/reading-sessions - Save a reading session
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { novelId, chapterId, session: readingSession } = readingSessionSchema.parse(body)

    // Verify novel and chapter exist
    const chapter = await prisma.chapter.findFirst({
      where: {
        id: chapterId,
        novelId,
        status: "PUBLISHED",
      },
      include: {
        novel: {
          select: {
            id: true,
            title: true,
          }
        }
      }
    })

    if (!chapter) {
      return NextResponse.json(
        { error: "Chapter not found or not published" },
        { status: 404 }
      )
    }

    // Update reading progress with session data
    const updatedProgress = await prisma.readingProgress.upsert({
      where: {
        userId_novelId: {
          userId: session.user.id,
          novelId,
        },
      },
      update: {
        chapterId,
        lastChapterId: chapterId,
        lastReadAt: readingSession.endTime || new Date(),
        totalTimeRead: {
          increment: readingSession.duration
        },
        // Store session metadata in a JSON field (if you add one) or calculate progress
        progress: Math.min(100, (readingSession.wordsRead / 2000) * 100), // Estimate progress
      },
      create: {
        userId: session.user.id,
        novelId,
        chapterId,
        lastChapterId: chapterId,
        lastReadAt: readingSession.endTime || new Date(),
        totalTimeRead: readingSession.duration,
        progress: Math.min(100, (readingSession.wordsRead / 2000) * 100),
      },
      include: {
        novel: {
          select: {
            id: true,
            title: true,
          }
        },
        chapter: {
          select: {
            id: true,
            title: true,
            order: true,
          }
        }
      }
    })

    // You could also create a separate ReadingSession model for detailed tracking
    // For now, we'll use the existing ReadingProgress model

    return NextResponse.json({
      success: true,
      session: {
        id: updatedProgress.id,
        ...readingSession,
        novel: updatedProgress.novel,
        chapter: updatedProgress.chapter,
      },
      message: "Reading session saved successfully",
    })

  } catch (error) {
    console.error("Error saving reading session:", error)
    return NextResponse.json(
      { error: "Failed to save reading session" },
      { status: 500 }
    )
  }
}
