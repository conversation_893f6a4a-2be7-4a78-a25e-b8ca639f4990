import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { supabase } from "@/lib/supabase"
import { createId } from "@paralleldrive/cuid2"
import { z } from "zod"

const readingSessionSchema = z.object({
  novelId: z.string(),
  chapterId: z.string(),
  session: z.object({
    id: z.string(),
    startTime: z.string().transform(str => new Date(str)),
    endTime: z.string().transform(str => new Date(str)).optional(),
    duration: z.number().int().min(0),
    chaptersRead: z.array(z.string()),
    wordsRead: z.number().int().min(0),
    averageReadingSpeed: z.number().min(0),
    breaks: z.number().int().min(0),
    focusScore: z.number().min(0).max(100),
  })
})

const querySchema = z.object({
  novelId: z.string().optional(),
  limit: z.string().optional().default("10"),
  offset: z.string().optional().default("0"),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
})

// GET /api/reading-sessions - Get user's reading sessions
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const query = querySchema.parse({
      novelId: searchParams.get("novelId") || undefined,
      limit: searchParams.get("limit") || "10",
      offset: searchParams.get("offset") || "0",
      startDate: searchParams.get("startDate") || undefined,
      endDate: searchParams.get("endDate") || undefined,
    })

    const limit = parseInt(query.limit)
    const offset = parseInt(query.offset)

    // Build Supabase query for reading progress
    let sessionsQuery = supabase
      .from('reading_progress')
      .select(`
        *,
        novels!reading_progress_novel_id_fkey(id, title, cover_image),
        chapters!reading_progress_chapter_id_fkey(id, title, order)
      `)
      .eq('user_id', session.user.id)
      .order('last_read_at', { ascending: false })
      .range(offset, offset + limit - 1)

    // Add novel filter if specified
    if (query.novelId) {
      sessionsQuery = sessionsQuery.eq('novel_id', query.novelId)
    }

    // Add date range filters if specified
    if (query.startDate) {
      sessionsQuery = sessionsQuery.gte('created_at', query.startDate)
    }
    if (query.endDate) {
      sessionsQuery = sessionsQuery.lte('created_at', query.endDate)
    }

    // Get reading sessions
    const { data: sessions, error: sessionsError } = await sessionsQuery

    if (sessionsError) {
      console.error("Error fetching reading sessions:", sessionsError)
      return NextResponse.json({ error: "Failed to fetch reading sessions" }, { status: 500 })
    }
    // Transform to session format (simplified for now)
    const formattedSessions = sessions?.map(progress => ({
      id: progress.id,
      startTime: progress.created_at,
      endTime: progress.last_read_at,
      duration: progress.total_time_read,
      chaptersRead: [progress.chapter_id].filter(Boolean),
      wordsRead: Math.floor(progress.total_time_read * 3.5), // Estimate: ~3.5 words per second
      averageReadingSpeed: progress.total_time_read > 0 ? Math.round((progress.total_time_read * 3.5) / (progress.total_time_read / 60)) : 0,
      breaks: Math.floor(progress.total_time_read / 1800), // Estimate breaks every 30 minutes
      focusScore: Math.max(60, 100 - Math.floor(progress.total_time_read / 1800) * 5), // Decrease focus with breaks
      novel: progress.novels ? {
        id: progress.novels.id,
        title: progress.novels.title,
        coverImage: progress.novels.cover_image,
      } : null,
      chapter: progress.chapters ? {
        id: progress.chapters.id,
        title: progress.chapters.title,
        order: progress.chapters.order,
      } : null,
      progress: progress.progress,
    })) || []

    return NextResponse.json({
      sessions: formattedSessions,
      pagination: {
        limit,
        offset,
        total: formattedSessions.length,
      }
    })

  } catch (error) {
    console.error("Error fetching reading sessions:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/reading-sessions - Save a reading session
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { novelId, chapterId, session: readingSession } = readingSessionSchema.parse(body)

    // Verify novel and chapter exist using Supabase
    const { data: chapter, error: chapterError } = await supabase
      .from('chapters')
      .select(`
        *,
        novels!chapters_novel_id_fkey(id, title)
      `)
      .eq('id', chapterId)
      .eq('novel_id', novelId)
      .eq('status', 'PUBLISHED')
      .single()

    if (chapterError || !chapter) {
      return NextResponse.json(
        { error: "Chapter not found or not published" },
        { status: 404 }
      )
    }

    // Check if reading progress exists
    const { data: existingProgress } = await supabase
      .from('reading_progress')
      .select('id, total_time_read')
      .eq('user_id', session.user.id)
      .eq('novel_id', novelId)
      .single()

    const now = new Date().toISOString()
    const endTime = readingSession.endTime || now
    const newProgress = Math.min(100, (readingSession.wordsRead / 2000) * 100)

    let updatedProgress

    if (existingProgress) {
      // Update existing progress
      const { data, error } = await supabase
        .from('reading_progress')
        .update({
          chapter_id: chapterId,
          last_chapter_id: chapterId,
          last_read_at: endTime,
          total_time_read: existingProgress.total_time_read + readingSession.duration,
          progress: newProgress,
          updated_at: now,
        })
        .eq('user_id', session.user.id)
        .eq('novel_id', novelId)
        .select(`
          *,
          novels!reading_progress_novel_id_fkey(id, title),
          chapters!reading_progress_chapter_id_fkey(id, title, order)
        `)
        .single()

      if (error) {
        console.error("Error updating reading progress:", error)
        return NextResponse.json({ error: "Failed to update reading progress" }, { status: 500 })
      }
      updatedProgress = data
    } else {
      // Create new progress
      const { data, error } = await supabase
        .from('reading_progress')
        .insert({
          id: createId(),
          user_id: session.user.id,
          novel_id: novelId,
          chapter_id: chapterId,
          last_chapter_id: chapterId,
          last_read_at: endTime,
          total_time_read: readingSession.duration,
          progress: newProgress,
          created_at: now,
          updated_at: now,
        })
        .select(`
          *,
          novels!reading_progress_novel_id_fkey(id, title),
          chapters!reading_progress_chapter_id_fkey(id, title, order)
        `)
        .single()

      if (error) {
        console.error("Error creating reading progress:", error)
        return NextResponse.json({ error: "Failed to create reading progress" }, { status: 500 })
      }
      updatedProgress = data
    }

    // You could also create a separate ReadingSession model for detailed tracking
    // For now, we'll use the existing ReadingProgress model

    // Transform response to match expected format
    const transformedProgress = {
      id: updatedProgress.id,
      userId: updatedProgress.user_id,
      novelId: updatedProgress.novel_id,
      chapterId: updatedProgress.chapter_id,
      lastChapterId: updatedProgress.last_chapter_id,
      progress: updatedProgress.progress,
      lastReadAt: updatedProgress.last_read_at,
      totalTimeRead: updatedProgress.total_time_read,
      createdAt: updatedProgress.created_at,
      updatedAt: updatedProgress.updated_at,
      novel: updatedProgress.novels,
      chapter: updatedProgress.chapters,
    }

    return NextResponse.json({
      success: true,
      session: {
        id: updatedProgress.id,
        ...readingSession,
        novel: updatedProgress.novels,
        chapter: updatedProgress.chapters,
      },
      progress: transformedProgress,
      message: "Reading session saved successfully",
    })

  } catch (error) {
    console.error("Error saving reading session:", error)
    return NextResponse.json(
      { error: "Failed to save reading session" },
      { status: 500 }
    )
  }
}
