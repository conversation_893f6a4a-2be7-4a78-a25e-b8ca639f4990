import { NextResponse } from "next/server"
import { supabase } from "@/lib/supabase"
import { SUBSCRIPTION_TIERS } from "@/lib/stripe"
import { createId } from "@paralleldrive/cuid2"

// GET /api/subscriptions/tiers - Get all subscription tier configurations
export async function GET() {
  try {
    // Get tier configurations from database using Supabase
    const { data: tierConfigs, error: tierError } = await supabase
      .from('subscription_tier_configs')
      .select('*')
      .eq('is_active', true)
      .order('tier', { ascending: true })

    if (tierError) {
      console.error("Error fetching tier configs:", tierError)
      return NextResponse.json({ error: "Failed to fetch tier configurations" }, { status: 500 })
    }

    // If no configurations exist in database, create them from constants
    if (!tierConfigs || tierConfigs.length === 0) {
      // Create default configurations using Supabase
      const defaultTiers = [
        {
          id: createId(),
          tier: 'FREE',
          name: SUBSCRIPTION_TIERS.FREE.name,
          description: SUBSCRIPTION_TIERS.FREE.description,
          price: SUBSCRIPTION_TIERS.FREE.price,
          yearly_price: SUBSCRIPTION_TIERS.FREE.yearlyPrice,
          features: SUBSCRIPTION_TIERS.FREE.features,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: createId(),
          tier: 'PREMIUM',
          name: SUBSCRIPTION_TIERS.PREMIUM.name,
          description: SUBSCRIPTION_TIERS.PREMIUM.description,
          price: SUBSCRIPTION_TIERS.PREMIUM.price,
          yearly_price: SUBSCRIPTION_TIERS.PREMIUM.yearlyPrice,
          features: SUBSCRIPTION_TIERS.PREMIUM.features,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: createId(),
          tier: 'PREMIUM_PLUS',
          name: SUBSCRIPTION_TIERS.PREMIUM_PLUS.name,
          description: SUBSCRIPTION_TIERS.PREMIUM_PLUS.description,
          price: SUBSCRIPTION_TIERS.PREMIUM_PLUS.price,
          yearly_price: SUBSCRIPTION_TIERS.PREMIUM_PLUS.yearlyPrice,
          features: SUBSCRIPTION_TIERS.PREMIUM_PLUS.features,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
      ]

      const { data: defaultConfigs, error: createError } = await supabase
        .from('subscription_tier_configs')
        .insert(defaultTiers)
        .select('*')

      if (createError) {
        console.error("Error creating default tier configs:", createError)
        return NextResponse.json({ error: "Failed to create tier configurations" }, { status: 500 })
      }

      // Transform to match expected format
      const transformedConfigs = defaultConfigs?.map(config => ({
        id: config.id,
        tier: config.tier,
        name: config.name,
        description: config.description,
        price: config.price,
        yearlyPrice: config.yearly_price,
        features: config.features,
        isActive: config.is_active,
        createdAt: config.created_at,
        updatedAt: config.updated_at,
      })) || []

      return NextResponse.json({ tiers: transformedConfigs })
    }

    // Transform existing configs to match expected format
    const transformedConfigs = tierConfigs.map(config => ({
      id: config.id,
      tier: config.tier,
      name: config.name,
      description: config.description,
      price: config.price,
      yearlyPrice: config.yearly_price,
      features: config.features,
      isActive: config.is_active,
      createdAt: config.created_at,
      updatedAt: config.updated_at,
    }))

    return NextResponse.json({ tiers: transformedConfigs })
  } catch (error) {
    console.error("Error fetching subscription tiers:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
